/**
 * Google Clone with Gemini AI Integration
 * Refactored for better maintainability and organization
 */

// ===== CONFIGURATION =====
const CONFIG = {
    GOOGLE_SEARCH_API_KEY: 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM',
    GOOGLE_SEARCH_CX: '30a8567a4e17d49d2',
    GEMINI_API_KEY: 'AIzaSyBLZPBER1n_SpWx5ZbK9ToP3VeD10K5IHI',
    RESULTS_PER_PAGE: 10,
    MAX_VISIBLE_PAGES: 10,
    DESKTOP_BREAKPOINT: 1024,
    GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent'
};

// ===== APPLICATION STATE =====
class AppState {
    constructor() {
        this.currentPage = 1;
        this.originalQuery = '';
        this.currentQueryForAPI = '';
        this.totalResults = 0;
        this.currentSearchResultsForSummary = [];
        this.currentActiveTab = 'all';
    }

    reset() {
        this.currentPage = 1;
        this.originalQuery = '';
        this.currentQueryForAPI = '';
        this.totalResults = 0;
        this.currentSearchResultsForSummary = [];
        this.currentActiveTab = 'all';
    }

    setQuery(query) {
        this.originalQuery = query;
        this.currentQueryForAPI = query;
    }

    setTab(tab) {
        this.currentActiveTab = tab;
    }

    setPage(page) {
        this.currentPage = page;
    }

    setResults(results) {
        this.totalResults = results;
    }

    setSummaryData(data) {
        this.currentSearchResultsForSummary = data;
    }
}

// ===== DOM ELEMENTS MANAGER =====
class DOMElements {
    constructor() {
        // Main search elements
        this.initialSearchSection = document.getElementById('initialSearchSection');
        this.searchForm = document.getElementById('searchForm');
        this.searchInput = document.getElementById('searchInput');
        this.talkToAIButton = document.getElementById('talkToAIButton');

        // Results header elements
        this.resultsHeader = document.getElementById('resultsHeader');
        this.resultsSearchForm = document.getElementById('resultsSearchForm');
        this.resultsSearchInput = document.getElementById('resultsSearchInput');
        this.searchTabsContainer = document.getElementById('searchTabsContainer');

        // Results area elements
        this.resultsAreaContainer = document.getElementById('resultsAreaContainer');
        this.resultsStats = document.getElementById('resultsStats');
        this.resultsContainer = document.getElementById('resultsContainer');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.messageArea = document.getElementById('messageArea');

        // AI Summary elements
        this.aiSummarySection = document.getElementById('aiSummarySection');
        this.aiSummaryContent = document.getElementById('aiSummaryContent');
        this.geminiSummaryLoader = document.getElementById('geminiSummaryLoader');

        // AI Talk elements
        this.aiTalkResponseSection = document.getElementById('aiTalkResponseSection');
        this.aiTalkResponseContent = document.getElementById('aiTalkResponseContent');
        this.geminiTalkLoader = document.getElementById('geminiTalkLoader');

        // People Also Ask elements
        this.paaContainer = document.getElementById('paaContainer');
        this.paaContent = document.getElementById('paaContent');
        this.geminiPAALoader = document.getElementById('geminiPAALoader');
        this.paaContainerMain = document.getElementById('paaContainerMain');
        this.paaContentMain = document.getElementById('paaContentMain');
        this.geminiPAALoaderMain = document.getElementById('geminiPAALoaderMain');

        // Related Searches elements
        this.relatedSearchesContainer = document.getElementById('relatedSearchesContainer');
        this.relatedSearchesContent = document.getElementById('relatedSearchesContent');
        this.geminiRelatedLoader = document.getElementById('geminiRelatedLoader');
        this.relatedSearchesSidebar = document.getElementById('relatedSearchesSidebar');
        this.relatedSearchesSidebarContent = document.getElementById('relatedSearchesSidebarContent');
        this.geminiRelatedSidebarLoader = document.getElementById('geminiRelatedSidebarLoader');

        // Pagination elements
        this.paginationContainer = document.getElementById('paginationContainer');
        this.prevPageLink = document.getElementById('prevPageLink');
        this.nextPageLink = document.getElementById('nextPageLink');
        this.pageNumbers = document.getElementById('pageNumbers');

        // New feature elements
        this.searchSuggestions = document.getElementById('searchSuggestions');
        this.resultsSearchSuggestions = document.getElementById('resultsSearchSuggestions');
        this.voiceSearchBtn = document.getElementById('voiceSearchBtn');
        this.cameraSearchBtn = document.getElementById('cameraSearchBtn');
        this.resultsVoiceSearchBtn = document.getElementById('resultsVoiceSearchBtn');
        this.resultsCameraSearchBtn = document.getElementById('resultsCameraSearchBtn');
        this.feelingLuckyButton = document.getElementById('feelingLuckyButton');
        this.searchFilters = document.getElementById('searchFilters');
        this.timeFilter = document.getElementById('timeFilter');
        this.regionFilter = document.getElementById('regionFilter');
        this.languageFilter = document.getElementById('languageFilter');
        this.quickAccess = document.getElementById('quickAccess');
        this.trendingList = document.getElementById('trendingList');
        this.searchHistoryList = document.getElementById('searchHistoryList');
        this.knowledgePanel = document.getElementById('knowledgePanel');
        this.calculatorWidget = document.getElementById('calculatorWidget');
        this.calculatorInput = document.getElementById('calculatorInput');
        this.calculatorResult = document.getElementById('calculatorResult');
        this.weatherWidget = document.getElementById('weatherWidget');
        this.weatherContent = document.getElementById('weatherContent');
    }

    hideAll(elements) {
        elements.forEach(element => {
            if (element) element.classList.add('hidden');
        });
    }

    showAll(elements) {
        elements.forEach(element => {
            if (element) element.classList.remove('hidden');
        });
    }

    clearContent(elements) {
        elements.forEach(element => {
            if (element) element.innerHTML = '';
        });
    }
}

// ===== UTILITY FUNCTIONS =====
class Utils {
    static extractYouTubeVideoId(url) {
        const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[7].length === 11) ? match[7] : null;
    }

    static isDesktop() {
        return window.innerWidth > CONFIG.DESKTOP_BREAKPOINT;
    }

    static formatURL(url) {
        try {
            const linkUrl = new URL(url);
            const domainPart = `<span class="domain">${linkUrl.hostname}</span>`;
            let pathPart = '';
            if (linkUrl.pathname && linkUrl.pathname !== '/') {
                const pathSegments = linkUrl.pathname.split('/').filter(Boolean)
                    .map(part => part.length > 20 ? part.substring(0, 17) + '...' : part);
                if (pathSegments.length > 0) {
                    pathPart = ` <span class="path">› ${pathSegments.join(' › ')}</span>`;
                }
            }
            return domainPart + pathPart;
        } catch (e) {
            return url;
        }
    }

    static createFavicon(domain) {
        return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    }

    static createYouTubeThumbnail(videoId) {
        return `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;
    }

    static createPlaceholderImage(width, height, text) {
        return `https://placehold.co/${width}x${height}/eee/ccc?text=${encodeURIComponent(text)}`;
    }
}

// ===== API SERVICE =====
class APIService {
    static async callGeminiAPI(prompt, useJSON = false, schema = null) {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = { contents: chatHistory };

        if (useJSON && schema) {
            payload.generationConfig = {
                responseMimeType: "application/json",
                responseSchema: schema
            };
        }

        const response = await fetch(`${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
        }

        const result = await response.json();
        if (!result.candidates?.[0]?.content?.parts?.[0]?.text) {
            throw new Error("Unexpected response structure from Gemini API");
        }

        return useJSON ? JSON.parse(result.candidates[0].content.parts[0].text) : result.candidates[0].content.parts[0].text;
    }

    static async fetchGoogleSearch(query, startIndex = 1, searchType = null) {
        if (!CONFIG.GOOGLE_SEARCH_API_KEY || !CONFIG.GOOGLE_SEARCH_CX) {
            throw new Error('Google Search API credentials not configured');
        }

        let apiUrl = `https://www.googleapis.com/customsearch/v1?key=${CONFIG.GOOGLE_SEARCH_API_KEY}&cx=${CONFIG.GOOGLE_SEARCH_CX}&q=${encodeURIComponent(query)}&start=${startIndex}&num=${CONFIG.RESULTS_PER_PAGE}`;
        
        if (searchType === 'image') {
            apiUrl += '&searchType=image';
        }

        const response = await fetch(apiUrl);
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Google Search API error: ${response.status} ${response.statusText} - ${errorData.error?.message || ''}`);
        }

        return response.json();
    }
}

// ===== UI MANAGER =====
class UIManager {
    constructor(domElements, appState) {
        this.dom = domElements;
        this.state = appState;
    }

    showMainSearchPage() {
        this.dom.hideAll([
            this.dom.resultsHeader,
            this.dom.searchTabsContainer,
            this.dom.resultsAreaContainer,
            this.dom.aiTalkResponseSection
        ]);

        this.dom.showAll([this.dom.initialSearchSection]);

        document.body.classList.remove('results-active', 'ai-talk-active');

        this.clearAllResults();
        this.state.reset();

        this.dom.searchInput.value = '';
        this.dom.resultsSearchInput.value = '';
        this.dom.searchInput.focus();

        // Show quick access features
        this.showQuickAccess();
    }

    showQuickAccess() {
        if (this.dom.quickAccess) {
            this.dom.quickAccess.classList.remove('hidden');
            searchHistoryManager.displayHistory();
            trendingSearchesManager.displayTrending();
        }
    }

    hideQuickAccess() {
        if (this.dom.quickAccess) {
            this.dom.quickAccess.classList.add('hidden');
        }
    }

    showResultsPageLayout(queryToDisplay, isAiTalkMode = false) {
        this.dom.hideAll([this.dom.initialSearchSection]);
        this.dom.showAll([this.dom.resultsHeader]);
        this.dom.resultsSearchInput.value = queryToDisplay;

        // Hide quick access when showing results
        this.hideQuickAccess();

        if (isAiTalkMode) {
            document.body.classList.add('ai-talk-active');
            document.body.classList.remove('results-active');
            this.dom.hideAll([this.dom.searchTabsContainer, this.dom.resultsAreaContainer]);
            this.dom.showAll([this.dom.aiTalkResponseSection]);
        } else {
            document.body.classList.add('results-active');
            document.body.classList.remove('ai-talk-active');
            this.dom.showAll([this.dom.searchTabsContainer, this.dom.resultsAreaContainer]);
            this.dom.hideAll([
                this.dom.aiTalkResponseSection,
                this.dom.aiSummarySection,
                this.dom.paaContainer,
                this.dom.relatedSearchesContainer
            ]);
        }
    }

    clearAllResults() {
        this.dom.clearContent([
            this.dom.resultsContainer,
            this.dom.aiSummaryContent,
            this.dom.paaContent,
            this.dom.paaContentMain,
            this.dom.relatedSearchesContent,
            this.dom.relatedSearchesSidebarContent,
            this.dom.aiTalkResponseContent
        ]);

        this.dom.hideAll([
            this.dom.paginationContainer,
            this.dom.aiSummarySection,
            this.dom.paaContainer,
            this.dom.paaContainerMain,
            this.dom.relatedSearchesContainer,
            this.dom.relatedSearchesSidebar
        ]);

        this.dom.resultsStats.textContent = '';
        this.dom.messageArea.textContent = '';
    }

    showMessage(text, isError = false) {
        this.dom.messageArea.textContent = text;
        this.dom.messageArea.style.color = isError ? 'red' : '#70757a';
    }

    setActiveTab(tabName) {
        document.querySelectorAll('#searchTabs .tab-item').forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.tabName === tabName) {
                tab.classList.add('active');
            }
        });
    }

    updateResultsStats(searchInfo) {
        if (searchInfo) {
            this.dom.resultsStats.textContent =
                `About ${searchInfo.formattedTotalResults} results (${searchInfo.formattedSearchTime} seconds)`;
        } else {
            this.dom.resultsStats.textContent = '';
        }
    }

    showLoading() {
        this.dom.loadingIndicator.classList.remove('hidden');
    }

    hideLoading() {
        this.dom.loadingIndicator.classList.add('hidden');
    }

    manageSidebarLayout() {
        if (this.state.currentActiveTab === 'all') {
            if (Utils.isDesktop()) {
                // Desktop: show in sidebar
                this.dom.showAll([this.dom.paaContainer, this.dom.relatedSearchesSidebar]);
                this.dom.hideAll([this.dom.paaContainerMain, this.dom.relatedSearchesContainer]);
            } else {
                // Mobile: show in main content
                this.dom.hideAll([this.dom.paaContainer, this.dom.relatedSearchesSidebar]);
                this.dom.showAll([this.dom.paaContainerMain, this.dom.relatedSearchesContainer]);
            }
        } else {
            // Other tabs: hide all PAA and related searches
            this.dom.hideAll([
                this.dom.paaContainer,
                this.dom.paaContainerMain,
                this.dom.relatedSearchesSidebar,
                this.dom.relatedSearchesContainer
            ]);
        }
    }
}

// ===== SEARCH MANAGER =====
class SearchManager {
    constructor(domElements, appState, uiManager) {
        this.dom = domElements;
        this.state = appState;
        this.ui = uiManager;
    }

    async performSearch(query, isLucky = false) {
        if (!query.trim()) {
            this.dom.searchInput.focus();
            return;
        }

        this.state.setQuery(query.trim());
        this.state.setPage(1);
        this.state.setTab('all');
        this.ui.setActiveTab('all');

        await this.fetchCurrentTabData();
    }

    async fetchCurrentTabData() {
        if (!this.state.originalQuery) return;

        if (document.body.classList.contains('ai-talk-active')) {
            document.body.classList.remove('ai-talk-active');
            this.dom.aiTalkResponseSection.classList.add('hidden');
            this.state.setTab('all');
            this.ui.setActiveTab('all');
        }

        const { queryForAPI, searchType } = this.buildSearchQuery();
        await this.fetchGoogleResults(queryForAPI, false,
            (this.state.currentPage - 1) * CONFIG.RESULTS_PER_PAGE + 1, searchType);
    }

    buildSearchQuery() {
        let queryForAPI = this.state.originalQuery;
        let searchType = null;

        switch (this.state.currentActiveTab) {
            case 'images':
                searchType = 'image';
                break;
            case 'news':
                queryForAPI += ' latest news';
                break;
            case 'videos':
                queryForAPI += ' videos';
                break;
            case 'forums':
                queryForAPI += ' site:reddit.com OR site:quora.com OR inurl:forum OR inurl:viewthread OR intitle:forum';
                break;
        }

        return { queryForAPI, searchType };
    }

    async fetchGoogleResults(queryForAPI, isFeelingLucky = false, startIndex = 1, searchTypeParam = null) {
        try {
            this.ui.showResultsPageLayout(this.state.originalQuery, false);
            this.ui.clearAllResults();
            this.ui.showLoading();

            const data = await APIService.fetchGoogleSearch(queryForAPI, startIndex, searchTypeParam);

            this.ui.updateResultsStats(data.searchInformation);
            this.state.setResults(parseInt(data.searchInformation?.totalResults || 0));

            if (data.items && data.items.length > 0) {
                if (isFeelingLucky) {
                    window.location.href = data.items[0].link;
                    return;
                }

                const resultRenderer = new ResultRenderer(this.dom, this.state);
                resultRenderer.displayResults(data.items, searchTypeParam);

                this.updatePagination();
                await this.handleAIFeatures(data.items);
            } else {
                const message = startIndex === 1
                    ? `No ${searchTypeParam || 'web'} results found for "${this.state.originalQuery}".`
                    : 'No more results.';
                this.ui.showMessage(message);
                this.state.setResults(0);
                this.updatePagination();
            }
        } catch (error) {
            console.error('Failed to fetch Google search results:', error);
            this.ui.showMessage(`${error.message}. Check console.`, true);
            this.state.setResults(0);
            this.updatePagination();
        } finally {
            this.ui.hideLoading();
        }
    }

    async handleAIFeatures(items) {
        if (this.state.currentActiveTab === 'all') {
            const summaryData = items.map(item => ({ title: item.title, snippet: item.snippet }));
            this.state.setSummaryData(summaryData);

            const aiManager = new AIManager(this.dom, this.state, this.ui);
            await aiManager.generateAISummary(this.state.originalQuery, summaryData);
            await aiManager.generatePeopleAlsoAsk(this.state.originalQuery);
            await aiManager.generateRelatedSearches(this.state.originalQuery);

            // Handle special queries
            await this.handleSpecialQueries(this.state.originalQuery);
        }

        this.ui.manageSidebarLayout();
    }

    async handleSpecialQueries(query) {
        const lowerQuery = query.toLowerCase();

        // Calculator queries
        if (this.isCalculatorQuery(lowerQuery)) {
            calculatorManager.show();
            calculatorManager.calculate(query);
        } else {
            calculatorManager.hide();
        }

        // Knowledge panel queries
        await knowledgePanelManager.generateKnowledgePanel(query);

        // Weather queries (placeholder)
        if (lowerQuery.includes('weather')) {
            this.showWeatherWidget();
        } else {
            this.hideWeatherWidget();
        }
    }

    isCalculatorQuery(query) {
        const mathPattern = /^[\d+\-*/().\s]+$/;
        const mathKeywords = ['calculate', 'math', 'plus', 'minus', 'times', 'divided by'];

        return mathPattern.test(query) || mathKeywords.some(keyword => query.includes(keyword));
    }

    showWeatherWidget() {
        this.dom.weatherWidget.classList.remove('hidden');
        this.dom.weatherContent.innerHTML = `
            <div class="weather-card">
                <div class="weather-icon">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="weather-info">
                    <div class="weather-temp">22°C</div>
                    <div class="weather-desc">Sunny</div>
                    <div class="weather-location">Current Location</div>
                </div>
            </div>
        `;
    }

    hideWeatherWidget() {
        this.dom.weatherWidget.classList.add('hidden');
    }

    updatePagination() {
        const paginationManager = new PaginationManager(this.dom, this.state, this);
        paginationManager.update();
    }
}

// ===== RESULT RENDERER =====
class ResultRenderer {
    constructor(domElements, appState) {
        this.dom = domElements;
        this.state = appState;
    }

    displayResults(items, searchType) {
        this.dom.resultsContainer.innerHTML = '';
        this.dom.resultsContainer.className = 'results-container';

        if (searchType === 'image') {
            this.dom.resultsContainer.classList.add('image-results-grid');
            this.renderImageResults(items);
        } else if (this.state.currentActiveTab === 'videos') {
            this.renderVideoResults(items);
        } else {
            this.renderWebResults(items);
        }
    }

    renderImageResults(items) {
        items.forEach(item => {
            const imageItemDiv = document.createElement('div');
            imageItemDiv.classList.add('image-result-item');

            const linkElement = document.createElement('a');
            linkElement.href = item.image.contextLink;
            linkElement.target = '_blank';

            const imgElement = document.createElement('img');
            imgElement.src = item.image.thumbnailLink || item.link;
            imgElement.alt = item.title;
            imgElement.loading = 'lazy';
            imgElement.onerror = () => {
                imgElement.src = Utils.createPlaceholderImage(150, 120, 'No Image');
            };

            const titleDiv = document.createElement('div');
            titleDiv.classList.add('image-title');
            titleDiv.textContent = item.title;

            linkElement.appendChild(imgElement);
            linkElement.appendChild(titleDiv);
            imageItemDiv.appendChild(linkElement);
            this.dom.resultsContainer.appendChild(imageItemDiv);
        });
    }

    renderVideoResults(items) {
        items.forEach(item => {
            const videoItemDiv = document.createElement('div');
            videoItemDiv.classList.add('video-result-item');

            // Create thumbnail container
            const thumbnailContainer = this.createVideoThumbnail(item);
            const contentContainer = this.createVideoContent(item);

            videoItemDiv.appendChild(thumbnailContainer);
            videoItemDiv.appendChild(contentContainer);
            this.dom.resultsContainer.appendChild(videoItemDiv);
        });
    }

    createVideoThumbnail(item) {
        const thumbnailContainer = document.createElement('div');
        thumbnailContainer.classList.add('video-thumbnail-container');

        const thumbnailLink = document.createElement('a');
        thumbnailLink.href = item.link;
        thumbnailLink.target = '_blank';

        const thumbnailImg = document.createElement('img');
        thumbnailImg.classList.add('video-thumbnail');
        thumbnailImg.loading = 'lazy';

        // Try to extract video ID and create YouTube thumbnail
        let thumbnailUrl = '';
        if (item.link.includes('youtube.com/watch?v=') || item.link.includes('youtu.be/')) {
            const videoId = Utils.extractYouTubeVideoId(item.link);
            if (videoId) {
                thumbnailUrl = Utils.createYouTubeThumbnail(videoId);
            }
        }

        if (!thumbnailUrl) {
            thumbnailUrl = Utils.createPlaceholderImage(160, 90, '📹');
        }

        thumbnailImg.src = thumbnailUrl;
        thumbnailImg.alt = item.title;
        thumbnailImg.onerror = () => {
            thumbnailImg.src = Utils.createPlaceholderImage(160, 90, '📹');
        };

        // Add play icon overlay
        const playIcon = document.createElement('i');
        playIcon.classList.add('fas', 'fa-play', 'video-play-icon');

        thumbnailLink.appendChild(thumbnailImg);
        thumbnailLink.appendChild(playIcon);
        thumbnailContainer.appendChild(thumbnailLink);

        return thumbnailContainer;
    }

    createVideoContent(item) {
        const contentContainer = document.createElement('div');
        contentContainer.classList.add('video-content');

        const titleElement = document.createElement('a');
        titleElement.classList.add('title');
        titleElement.href = item.link;
        titleElement.textContent = item.title;
        titleElement.target = '_blank';

        const urlElement = document.createElement('div');
        urlElement.classList.add('url');
        try {
            const linkUrl = new URL(item.link);
            urlElement.textContent = linkUrl.hostname;
        } catch (e) {
            urlElement.textContent = item.displayLink || item.link;
        }

        const snippetElement = document.createElement('div');
        snippetElement.classList.add('snippet');
        snippetElement.innerHTML = item.htmlSnippet || item.snippet;

        contentContainer.appendChild(titleElement);
        contentContainer.appendChild(urlElement);
        contentContainer.appendChild(snippetElement);

        return contentContainer;
    }

    renderWebResults(items) {
        items.forEach(item => {
            const resultItemDiv = document.createElement('div');
            resultItemDiv.classList.add('result-item');

            const urlLineDiv = this.createUrlLine(item);
            const titleElement = this.createTitle(item);
            const snippetElement = this.createSnippet(item);

            resultItemDiv.appendChild(urlLineDiv);
            resultItemDiv.appendChild(titleElement);
            resultItemDiv.appendChild(snippetElement);
            this.dom.resultsContainer.appendChild(resultItemDiv);
        });
    }

    createUrlLine(item) {
        const urlLineDiv = document.createElement('div');
        urlLineDiv.classList.add('url-line');

        const faviconImg = document.createElement('img');
        faviconImg.classList.add('favicon');
        faviconImg.loading = 'lazy';
        faviconImg.onerror = () => { faviconImg.style.display = 'none'; };

        try {
            const domain = new URL(item.link).hostname;
            faviconImg.src = Utils.createFavicon(domain);
        } catch (e) {
            faviconImg.style.display = 'none';
        }

        const urlElement = document.createElement('span');
        urlElement.classList.add('url');
        urlElement.innerHTML = Utils.formatURL(item.link);

        urlLineDiv.appendChild(faviconImg);
        urlLineDiv.appendChild(urlElement);
        return urlLineDiv;
    }

    createTitle(item) {
        const titleElement = document.createElement('a');
        titleElement.classList.add('title');
        titleElement.href = item.link;
        titleElement.textContent = item.title;
        titleElement.target = '_blank';
        return titleElement;
    }

    createSnippet(item) {
        const snippetElement = document.createElement('p');
        snippetElement.classList.add('snippet');
        snippetElement.innerHTML = item.htmlSnippet || item.snippet;
        return snippetElement;
    }
}

// ===== AI MANAGER =====
class AIManager {
    constructor(domElements, appState, uiManager) {
        this.dom = domElements;
        this.state = appState;
        this.ui = uiManager;
    }

    async generateAISummary(query, resultsForSummary) {
        if (resultsForSummary.length === 0) {
            this.dom.aiSummarySection.classList.add('hidden');
            return;
        }

        this.dom.aiSummarySection.classList.remove('hidden');
        this.dom.aiSummaryContent.textContent = '';
        this.dom.geminiSummaryLoader.classList.remove('hidden');

        try {
            const resultsText = resultsForSummary.map(r => `Title: ${r.title}\nSnippet: ${r.snippet}`).join('\n\n');
            const prompt = `Based on the following search results for the query "${query}", please provide a brief summary (2-4 sentences) of the main topics or findings. Focus on the collective information rather than individual links:\n\n${resultsText}`;

            const summaryText = await APIService.callGeminiAPI(prompt);
            this.dom.aiSummaryContent.textContent = summaryText;
        } catch (error) {
            console.error("Error generating AI summary:", error);
            this.dom.aiSummaryContent.textContent = `Error generating summary: ${error.message}`;
            this.dom.aiSummaryContent.style.color = 'red';
        } finally {
            this.dom.geminiSummaryLoader.classList.add('hidden');
        }
    }

    async generatePeopleAlsoAsk(query) {
        const isDesktop = Utils.isDesktop();
        const container = isDesktop ? this.dom.paaContainer : this.dom.paaContainerMain;
        const content = isDesktop ? this.dom.paaContent : this.dom.paaContentMain;
        const loader = isDesktop ? this.dom.geminiPAALoader : this.dom.geminiPAALoaderMain;

        container.classList.remove('hidden');
        content.innerHTML = '';
        loader.classList.remove('hidden');

        try {
            const prompt = `For the search query "${query}", generate 3-4 common questions that people also ask.`;
            const schema = { type: "OBJECT", properties: { "questions": { type: "ARRAY", items: { type: "STRING" } } } };

            const response = await APIService.callGeminiAPI(prompt, true, schema);

            if (response.questions && response.questions.length > 0) {
                this.displayPeopleAlsoAsk(response.questions, content);
            } else {
                content.textContent = "No 'People Also Ask' questions found.";
            }
        } catch (error) {
            console.error("Error fetching PAA questions:", error);
            content.textContent = `Could not load PAA questions: ${error.message}`;
            content.style.color = 'red';
        } finally {
            loader.classList.add('hidden');
        }
    }

    displayPeopleAlsoAsk(questions, container) {
        container.innerHTML = '';
        questions.forEach(questionText => {
            const paaItemDiv = document.createElement('div');
            paaItemDiv.classList.add('paa-item');

            const questionDiv = document.createElement('div');
            questionDiv.classList.add('paa-question');
            questionDiv.innerHTML = `<span>${questionText}</span><i class="fas fa-chevron-down toggle-icon"></i>`;

            const answerDiv = document.createElement('div');
            answerDiv.classList.add('paa-answer');

            paaItemDiv.appendChild(questionDiv);
            paaItemDiv.appendChild(answerDiv);
            container.appendChild(paaItemDiv);

            questionDiv.addEventListener('click', async () => {
                const isOpen = paaItemDiv.classList.toggle('open');
                if (isOpen && !answerDiv.dataset.loaded) {
                    await this.loadPAAAnswer(questionText, answerDiv);
                }
            });
        });
    }

    async loadPAAAnswer(questionText, answerDiv) {
        const answerLoader = document.createElement('div');
        answerLoader.classList.add('gemini-loading-spinner', 'inline-gemini-loader');
        answerDiv.innerHTML = '';
        answerDiv.appendChild(answerLoader);

        try {
            const prompt = `Answer the following question concisely, as if it were a "People Also Ask" result on Google: "${questionText}"`;
            const answerText = await APIService.callGeminiAPI(prompt);
            answerDiv.textContent = answerText;
            answerDiv.dataset.loaded = "true";
        } catch (error) {
            console.error("Error fetching PAA answer:", error);
            answerDiv.textContent = `Error loading answer: ${error.message}`;
            answerDiv.style.color = 'red';
        } finally {
            answerLoader.remove();
        }
    }

    async generateRelatedSearches(query) {
        const isDesktop = Utils.isDesktop();
        const container = isDesktop ? this.dom.relatedSearchesSidebar : this.dom.relatedSearchesContainer;
        const content = isDesktop ? this.dom.relatedSearchesSidebarContent : this.dom.relatedSearchesContent;
        const loader = isDesktop ? this.dom.geminiRelatedSidebarLoader : this.dom.geminiRelatedLoader;

        container.classList.remove('hidden');
        content.innerHTML = '';
        loader.classList.remove('hidden');

        try {
            const prompt = `Given the search query "${query}", suggest 4 highly relevant related search queries that a user might find helpful.`;
            const schema = { type: "OBJECT", properties: { "related_queries": { type: "ARRAY", items: { type: "STRING" } } } };

            const response = await APIService.callGeminiAPI(prompt, true, schema);

            if (response.related_queries && response.related_queries.length > 0) {
                this.displayRelatedSearches(response.related_queries, content, isDesktop);
            } else {
                content.textContent = "No AI related searches found.";
            }
        } catch (error) {
            console.error("Error fetching related searches:", error);
            content.textContent = `Could not load AI related searches: ${error.message}`;
            content.style.color = 'red';
        } finally {
            loader.classList.add('hidden');
        }
    }

    displayRelatedSearches(queries, container, isDesktop) {
        container.innerHTML = '';
        queries.forEach(queryText => {
            const item = document.createElement(isDesktop ? 'div' : 'span');
            item.classList.add('related-search-item');
            item.textContent = queryText;
            item.onclick = () => this.performRelatedSearch(queryText);
            container.appendChild(item);
        });
    }

    performRelatedSearch(queryText) {
        this.state.setQuery(queryText);
        this.dom.searchInput.value = queryText;
        this.dom.resultsSearchInput.value = queryText;
        this.state.setTab('all');
        this.ui.setActiveTab('all');
        this.state.setPage(1);

        const searchManager = new SearchManager(this.dom, this.state, this.ui);
        searchManager.fetchCurrentTabData();
    }

    async handleTalkToAI(query) {
        this.ui.showResultsPageLayout(query, true);
        this.ui.clearAllResults();
        this.dom.aiTalkResponseSection.classList.remove('hidden');
        this.dom.aiTalkResponseContent.innerHTML = '';
        this.dom.geminiTalkLoader.classList.remove('hidden');

        try {
            const prompt = `You are a helpful conversational AI. Respond to the following user query: "${query}"`;
            const responseText = await APIService.callGeminiAPI(prompt);
            this.dom.aiTalkResponseContent.textContent = responseText;
        } catch (error) {
            console.error("Error talking to AI:", error);
            this.dom.aiTalkResponseContent.textContent = `Error getting AI response: ${error.message}`;
            this.dom.aiTalkResponseContent.style.color = 'red';
        } finally {
            this.dom.geminiTalkLoader.classList.add('hidden');
        }
    }
}

// ===== PAGINATION MANAGER =====
class PaginationManager {
    constructor(domElements, appState, searchManager) {
        this.dom = domElements;
        this.state = appState;
        this.searchManager = searchManager;
    }

    update() {
        this.dom.pageNumbers.innerHTML = '';

        if (this.state.totalResults <= CONFIG.RESULTS_PER_PAGE) {
            this.dom.paginationContainer.classList.add('hidden');
            return;
        }

        this.dom.paginationContainer.classList.remove('hidden');
        const totalPages = Math.ceil(this.state.totalResults / CONFIG.RESULTS_PER_PAGE);

        this.updateNavigationButtons(totalPages);
        this.createPageNumbers(totalPages);
    }

    updateNavigationButtons(totalPages) {
        this.dom.prevPageLink.classList.toggle('disabled', this.state.currentPage === 1);
        this.dom.nextPageLink.classList.toggle('disabled',
            this.state.currentPage === totalPages || totalPages === 0);
    }

    createPageNumbers(totalPages) {
        const googleLetters = ['G', 'o', 'o', 'o', 'o', 'o', 'g', 'l', 'e'];
        const letterClasses = ['letter-g', 'letter-o1', 'letter-o2', 'letter-o3',
                              'letter-o2', 'letter-o1', 'letter-g2', 'letter-l', 'letter-e'];

        let startPage = Math.max(1, this.state.currentPage - Math.floor(CONFIG.MAX_VISIBLE_PAGES / 2));
        let endPage = Math.min(totalPages, startPage + CONFIG.MAX_VISIBLE_PAGES - 1);

        if (endPage - startPage + 1 < CONFIG.MAX_VISIBLE_PAGES && startPage > 1) {
            startPage = Math.max(1, endPage - CONFIG.MAX_VISIBLE_PAGES + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageLink = document.createElement('a');
            pageLink.href = '#';
            pageLink.classList.add('page-number');

            if (i < googleLetters.length + 1 && this.state.currentPage === i) {
                pageLink.textContent = googleLetters[i - 1];
                if (letterClasses[i - 1]) pageLink.classList.add(letterClasses[i - 1]);
            } else {
                pageLink.textContent = i;
            }

            if (i === this.state.currentPage) {
                pageLink.classList.add('current');
            }

            pageLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (i === this.state.currentPage) return;
                this.state.setPage(i);
                this.searchManager.fetchCurrentTabData();
            });

            this.dom.pageNumbers.appendChild(pageLink);
        }
    }
}

// ===== EVENT HANDLERS =====
class EventHandlers {
    constructor(domElements, appState, uiManager, searchManager) {
        this.dom = domElements;
        this.state = appState;
        this.ui = uiManager;
        this.searchManager = searchManager;
        this.aiManager = new AIManager(domElements, appState, uiManager);

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Main search form
        this.dom.searchForm.addEventListener('submit', (event) => {
            event.preventDefault();
            const query = this.dom.searchInput.value.trim();
            if (query) {
                searchHistoryManager.addToHistory(query);
                this.searchManager.performSearch(query);
            }
        });

        // Search input suggestions
        this.dom.searchInput.addEventListener('input', (e) => {
            searchSuggestionsManager.fetchSuggestions(e.target.value, this.dom.searchSuggestions);
        });

        this.dom.searchInput.addEventListener('keydown', (e) => {
            searchSuggestionsManager.handleKeyNavigation(e, this.dom.searchSuggestions);
        });

        this.dom.searchInput.addEventListener('blur', () => {
            setTimeout(() => searchSuggestionsManager.hideSuggestions(this.dom.searchSuggestions), 200);
        });

        // Results search input suggestions
        this.dom.resultsSearchInput.addEventListener('input', (e) => {
            searchSuggestionsManager.fetchSuggestions(e.target.value, this.dom.resultsSearchSuggestions);
        });

        this.dom.resultsSearchInput.addEventListener('keydown', (e) => {
            searchSuggestionsManager.handleKeyNavigation(e, this.dom.resultsSearchSuggestions);
        });

        this.dom.resultsSearchInput.addEventListener('blur', () => {
            setTimeout(() => searchSuggestionsManager.hideSuggestions(this.dom.resultsSearchSuggestions), 200);
        });

        // Voice search buttons
        this.dom.voiceSearchBtn?.addEventListener('click', () => {
            voiceSearchManager.startListening(this.dom.searchInput, this.dom.voiceSearchBtn);
        });

        this.dom.resultsVoiceSearchBtn?.addEventListener('click', () => {
            voiceSearchManager.startListening(this.dom.resultsSearchInput, this.dom.resultsVoiceSearchBtn);
        });

        // Camera search buttons (placeholder functionality)
        this.dom.cameraSearchBtn?.addEventListener('click', () => {
            alert('Camera search functionality would integrate with Google Lens API or similar image recognition service.');
        });

        this.dom.resultsCameraSearchBtn?.addEventListener('click', () => {
            alert('Camera search functionality would integrate with Google Lens API or similar image recognition service.');
        });

        // Feeling Lucky button
        this.dom.feelingLuckyButton?.addEventListener('click', () => {
            const query = this.dom.searchInput.value.trim();
            if (query) {
                searchHistoryManager.addToHistory(query);
                this.searchManager.performSearch(query, true); // true for feeling lucky
            }
        });

        // Talk to AI button
        this.dom.talkToAIButton.addEventListener('click', () => {
            const query = this.dom.searchInput.value.trim();
            if (query) {
                this.aiManager.handleTalkToAI(query);
            } else {
                this.ui.showMessage("Please enter something to talk about.");
            }
        });

        // Results search form
        this.dom.resultsSearchForm.addEventListener('submit', (event) => {
            event.preventDefault();
            const query = this.dom.resultsSearchInput.value.trim();

            if (document.body.classList.contains('ai-talk-active')) {
                if (query) {
                    this.aiManager.handleTalkToAI(query);
                } else {
                    this.ui.showMainSearchPage();
                }
            } else {
                if (query) {
                    this.state.setTab('all');
                    this.ui.setActiveTab('all');
                    this.searchManager.performSearch(query);
                } else {
                    this.ui.showMainSearchPage();
                }
            }
        });

        // Pagination
        this.dom.prevPageLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (this.state.currentPage > 1 && !this.dom.prevPageLink.classList.contains('disabled')) {
                this.state.setPage(this.state.currentPage - 1);
                this.searchManager.fetchCurrentTabData();
            }
        });

        this.dom.nextPageLink.addEventListener('click', (e) => {
            e.preventDefault();
            const maxPage = Math.ceil(this.state.totalResults / CONFIG.RESULTS_PER_PAGE);
            if (this.state.currentPage < maxPage && !this.dom.nextPageLink.classList.contains('disabled')) {
                this.state.setPage(this.state.currentPage + 1);
                this.searchManager.fetchCurrentTabData();
            }
        });

        // Window resize for responsive layout
        window.addEventListener('resize', () => {
            if (this.state.currentActiveTab === 'all' && this.state.originalQuery &&
                document.body.classList.contains('results-active')) {
                this.ui.manageSidebarLayout();

                // Re-fetch AI content if containers are empty
                if (Utils.isDesktop()) {
                    if (this.dom.paaContent.innerHTML === '') {
                        this.aiManager.generatePeopleAlsoAsk(this.state.originalQuery);
                    }
                    if (this.dom.relatedSearchesSidebarContent.innerHTML === '') {
                        this.aiManager.generateRelatedSearches(this.state.originalQuery);
                    }
                } else {
                    if (this.dom.paaContentMain.innerHTML === '') {
                        this.aiManager.generatePeopleAlsoAsk(this.state.originalQuery);
                    }
                    if (this.dom.relatedSearchesContent.innerHTML === '') {
                        this.aiManager.generateRelatedSearches(this.state.originalQuery);
                    }
                }
            }
        });
    }

    handleTabClick(tabName) {
        if (!this.state.originalQuery) {
            this.ui.showMessage("Please perform a search first.");
            this.ui.setActiveTab(this.state.currentActiveTab || 'all');
            return;
        }

        if (document.body.classList.contains('ai-talk-active')) {
            document.body.classList.remove('ai-talk-active');
            this.dom.aiTalkResponseSection.classList.add('hidden');
            this.dom.resultsAreaContainer.classList.remove('hidden');
        }

        this.state.setTab(tabName);
        this.ui.setActiveTab(tabName);
        this.state.setPage(1);
        this.searchManager.fetchCurrentTabData();
    }

    handleToolsClick() {
        this.ui.showMessage("Tools functionality is not implemented in this clone.");
    }
}

// ===== SEARCH SUGGESTIONS MANAGER =====
class SearchSuggestionsManager {
    constructor(domElements) {
        this.dom = domElements;
        this.currentSuggestions = [];
        this.selectedIndex = -1;
        this.debounceTimer = null;
    }

    async fetchSuggestions(query, targetElement) {
        if (!query.trim()) {
            this.hideSuggestions(targetElement);
            return;
        }

        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(async () => {
            try {
                // Use Google's suggestion API
                const response = await fetch(`https://suggestqueries.google.com/complete/search?client=firefox&q=${encodeURIComponent(query)}`);
                const data = await response.json();

                if (data && data[1]) {
                    this.displaySuggestions(data[1], query, targetElement);
                }
            } catch (error) {
                // Fallback to predefined suggestions
                this.displayFallbackSuggestions(query, targetElement);
            }
        }, 300);
    }

    displaySuggestions(suggestions, query, targetElement) {
        this.currentSuggestions = suggestions.slice(0, 8);
        targetElement.innerHTML = '';

        if (this.currentSuggestions.length === 0) {
            this.hideSuggestions(targetElement);
            return;
        }

        this.currentSuggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.innerHTML = `
                <i class="fas fa-search suggestion-icon"></i>
                <span class="suggestion-text">${this.highlightQuery(suggestion, query)}</span>
            `;

            item.addEventListener('click', () => {
                this.selectSuggestion(suggestion);
            });

            targetElement.appendChild(item);
        });

        targetElement.classList.remove('hidden');
    }

    displayFallbackSuggestions(query, targetElement) {
        const fallbackSuggestions = [
            `${query} meaning`,
            `${query} definition`,
            `${query} examples`,
            `${query} tutorial`,
            `${query} guide`,
            `${query} tips`
        ];
        this.displaySuggestions(fallbackSuggestions, query, targetElement);
    }

    highlightQuery(suggestion, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return suggestion.replace(regex, '<strong>$1</strong>');
    }

    hideSuggestions(targetElement) {
        targetElement.classList.add('hidden');
        this.currentSuggestions = [];
        this.selectedIndex = -1;
    }

    selectSuggestion(suggestion) {
        const searchManager = new SearchManager(this.dom, appState, uiManager);
        searchManager.performSearch(suggestion);
        this.hideSuggestions(this.dom.searchSuggestions);
        this.hideSuggestions(this.dom.resultsSearchSuggestions);
    }

    handleKeyNavigation(event, targetElement) {
        if (this.currentSuggestions.length === 0) return;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.currentSuggestions.length - 1);
                this.updateSelection(targetElement);
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelection(targetElement);
                break;
            case 'Enter':
                if (this.selectedIndex >= 0) {
                    event.preventDefault();
                    this.selectSuggestion(this.currentSuggestions[this.selectedIndex]);
                }
                break;
            case 'Escape':
                this.hideSuggestions(targetElement);
                break;
        }
    }

    updateSelection(targetElement) {
        const items = targetElement.querySelectorAll('.suggestion-item');
        items.forEach((item, index) => {
            item.classList.toggle('selected', index === this.selectedIndex);
        });
    }
}

// ===== VOICE SEARCH MANAGER =====
class VoiceSearchManager {
    constructor(domElements) {
        this.dom = domElements;
        this.recognition = null;
        this.isListening = false;
        this.initializeVoiceRecognition();
    }

    initializeVoiceRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';
        }
    }

    startListening(inputElement, buttonElement) {
        if (!this.recognition) {
            alert('Speech recognition is not supported in your browser.');
            return;
        }

        if (this.isListening) {
            this.stopListening(buttonElement);
            return;
        }

        this.isListening = true;
        buttonElement.classList.add('listening');
        inputElement.placeholder = 'Listening...';

        this.recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            inputElement.value = transcript;
            this.stopListening(buttonElement);

            // Automatically search
            const searchManager = new SearchManager(this.dom, appState, uiManager);
            searchManager.performSearch(transcript);
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.stopListening(buttonElement);
            alert('Speech recognition error. Please try again.');
        };

        this.recognition.onend = () => {
            this.stopListening(buttonElement);
        };

        this.recognition.start();
    }

    stopListening(buttonElement) {
        this.isListening = false;
        buttonElement.classList.remove('listening');
        this.dom.searchInput.placeholder = '';
        this.dom.resultsSearchInput.placeholder = '';

        if (this.recognition) {
            this.recognition.stop();
        }
    }
}

// Initialize global instances
const appState = new AppState();
const domElements = new DOMElements();
const uiManager = new UIManager(domElements, appState);
const searchManager = new SearchManager(domElements, appState, uiManager);
const searchSuggestionsManager = new SearchSuggestionsManager(domElements);
const voiceSearchManager = new VoiceSearchManager(domElements);

// ===== SEARCH HISTORY MANAGER =====
class SearchHistoryManager {
    constructor() {
        this.maxHistory = 10;
        this.storageKey = 'googleCloneHistory';
    }

    addToHistory(query) {
        if (!query.trim()) return;

        let history = this.getHistory();
        history = history.filter(item => item !== query); // Remove duplicates
        history.unshift(query); // Add to beginning
        history = history.slice(0, this.maxHistory); // Limit size

        localStorage.setItem(this.storageKey, JSON.stringify(history));
        this.displayHistory();
    }

    getHistory() {
        try {
            return JSON.parse(localStorage.getItem(this.storageKey)) || [];
        } catch {
            return [];
        }
    }

    clearHistory() {
        localStorage.removeItem(this.storageKey);
        this.displayHistory();
    }

    removeFromHistory(query) {
        let history = this.getHistory();
        history = history.filter(item => item !== query);
        localStorage.setItem(this.storageKey, JSON.stringify(history));
        this.displayHistory();
    }

    displayHistory() {
        const historyList = domElements.searchHistoryList;
        if (!historyList) return;

        const history = this.getHistory();
        historyList.innerHTML = '';

        if (history.length === 0) {
            historyList.innerHTML = '<div class="no-history">No recent searches</div>';
            return;
        }

        history.forEach(query => {
            const item = document.createElement('div');
            item.className = 'history-item';
            item.innerHTML = `
                <span class="history-text">${query}</span>
                <i class="fas fa-times delete-history" title="Remove from history"></i>
            `;

            item.querySelector('.history-text').addEventListener('click', () => {
                const searchManager = new SearchManager(domElements, appState, uiManager);
                searchManager.performSearch(query);
            });

            item.querySelector('.delete-history').addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeFromHistory(query);
            });

            historyList.appendChild(item);
        });
    }
}

// ===== TRENDING SEARCHES MANAGER =====
class TrendingSearchesManager {
    constructor() {
        this.trendingQueries = [
            'artificial intelligence',
            'climate change',
            'cryptocurrency',
            'space exploration',
            'renewable energy',
            'machine learning',
            'quantum computing',
            'virtual reality'
        ];
    }

    displayTrending() {
        const trendingList = domElements.trendingList;
        if (!trendingList) return;

        trendingList.innerHTML = '';

        // Shuffle and take 6 random trending topics
        const shuffled = [...this.trendingQueries].sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, 6);

        selected.forEach(query => {
            const item = document.createElement('div');
            item.className = 'trending-item';
            item.innerHTML = `
                <span class="trending-text">${query}</span>
                <i class="fas fa-trending-up trend-icon"></i>
            `;

            item.addEventListener('click', () => {
                const searchManager = new SearchManager(domElements, appState, uiManager);
                searchManager.performSearch(query);
            });

            trendingList.appendChild(item);
        });
    }
}

// ===== CALCULATOR MANAGER =====
class CalculatorManager {
    constructor(domElements) {
        this.dom = domElements;
        this.setupCalculator();
    }

    setupCalculator() {
        if (!this.dom.calculatorInput) return;

        this.dom.calculatorInput.addEventListener('input', (e) => {
            this.calculate(e.target.value);
        });

        this.dom.calculatorInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.calculate(e.target.value);
            }
        });
    }

    calculate(expression) {
        if (!expression.trim()) {
            this.dom.calculatorResult.textContent = '';
            return;
        }

        try {
            // Basic math operations
            const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');

            if (sanitized !== expression) {
                this.dom.calculatorResult.textContent = 'Invalid expression';
                return;
            }

            // Use Function constructor for safe evaluation
            const result = Function(`"use strict"; return (${sanitized})`)();

            if (isNaN(result) || !isFinite(result)) {
                this.dom.calculatorResult.textContent = 'Invalid calculation';
            } else {
                this.dom.calculatorResult.textContent = result.toLocaleString();
            }
        } catch (error) {
            this.dom.calculatorResult.textContent = 'Error';
        }
    }

    show() {
        this.dom.calculatorWidget.classList.remove('hidden');
    }

    hide() {
        this.dom.calculatorWidget.classList.add('hidden');
    }
}

// ===== KNOWLEDGE PANEL MANAGER =====
class KnowledgePanelManager {
    constructor(domElements) {
        this.dom = domElements;
    }

    async generateKnowledgePanel(query) {
        // Check if query might benefit from a knowledge panel
        const knowledgeKeywords = ['who is', 'what is', 'when was', 'where is', 'biography', 'history of'];
        const hasKnowledgeIntent = knowledgeKeywords.some(keyword =>
            query.toLowerCase().includes(keyword)
        );

        if (!hasKnowledgeIntent) {
            this.hide();
            return;
        }

        try {
            const prompt = `Create a knowledge panel for "${query}". Provide structured information including title, description, and key facts. Format as JSON with: title, description, facts (array of {label, value} objects), and image_description.`;

            const response = await APIService.callGeminiAPI(prompt, true, {
                type: "OBJECT",
                properties: {
                    title: { type: "STRING" },
                    description: { type: "STRING" },
                    facts: {
                        type: "ARRAY",
                        items: {
                            type: "OBJECT",
                            properties: {
                                label: { type: "STRING" },
                                value: { type: "STRING" }
                            }
                        }
                    },
                    image_description: { type: "STRING" }
                }
            });

            this.displayKnowledgePanel(response);
        } catch (error) {
            console.error('Error generating knowledge panel:', error);
            this.hide();
        }
    }

    displayKnowledgePanel(data) {
        const content = this.dom.knowledgePanel.querySelector('#knowledgePanelContent') ||
                       (() => {
                           const div = document.createElement('div');
                           div.id = 'knowledgePanelContent';
                           this.dom.knowledgePanel.appendChild(div);
                           return div;
                       })();

        content.innerHTML = `
            <div class="knowledge-card">
                <div class="knowledge-header">
                    <div class="knowledge-info">
                        <div class="knowledge-title">${data.title}</div>
                        <div class="knowledge-subtitle">${data.description}</div>
                    </div>
                </div>
                <div class="knowledge-facts">
                    ${data.facts.map(fact => `
                        <div class="knowledge-fact">
                            <span class="fact-label">${fact.label}</span>
                            <span class="fact-value">${fact.value}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        this.show();
    }

    show() {
        this.dom.knowledgePanel.classList.remove('hidden');
    }

    hide() {
        this.dom.knowledgePanel.classList.add('hidden');
    }
}

const searchHistoryManager = new SearchHistoryManager();
const trendingSearchesManager = new TrendingSearchesManager();
const calculatorManager = new CalculatorManager(domElements);
const knowledgePanelManager = new KnowledgePanelManager(domElements);
const eventHandlers = new EventHandlers(domElements, appState, uiManager, searchManager);

// Global functions for HTML onclick handlers
window.handleTabClick = (event, tabName) => eventHandlers.handleTabClick(tabName);
window.handleToolsClick = () => {
    const filtersElement = domElements.searchFilters;
    if (filtersElement) {
        filtersElement.classList.toggle('hidden');
    } else {
        eventHandlers.handleToolsClick();
    }
};
window.showMainSearchPage = () => uiManager.showMainSearchPage();

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    uiManager.showMainSearchPage();

    // Initialize trending searches and history
    if (domElements.trendingList) {
        trendingSearchesManager.displayTrending();
    }
    if (domElements.searchHistoryList) {
        searchHistoryManager.displayHistory();
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Focus search input with Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (document.body.classList.contains('results-active')) {
                domElements.resultsSearchInput.focus();
                domElements.resultsSearchInput.select();
            } else {
                domElements.searchInput.focus();
                domElements.searchInput.select();
            }
        }

        // Clear search with Escape
        if (e.key === 'Escape') {
            if (document.activeElement === domElements.searchInput) {
                domElements.searchInput.value = '';
                searchSuggestionsManager.hideSuggestions(domElements.searchSuggestions);
            } else if (document.activeElement === domElements.resultsSearchInput) {
                domElements.resultsSearchInput.value = '';
                searchSuggestionsManager.hideSuggestions(domElements.resultsSearchSuggestions);
            }
        }
    });

    console.log('Google Clone with enhanced features loaded successfully!');
});

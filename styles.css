body {
    font-family: 'Inter', sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #fff;
    color: #202124;
    margin: 0;
}

.main-content-centered {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

body.results-active .main-content-centered,
body.ai-talk-active .main-content-centered {
    padding-top: 125px;
    justify-content: flex-start;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 584px;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    height: 46px;
    padding: 0 15px;
    background-color: #fff;
}

.search-input-container:hover,
.search-input-container:focus-within {
    border-color: #dfe1e5;
    box-shadow: 0 1px 6px rgba(32,33,36,.28);
}

.search-icon {
    color: #9aa0a6;
    margin-right: 10px;
}

.search-input {
    flex-grow: 1;
    border: none;
    outline: none;
    font-size: 16px;
    background-color: transparent;
}

.search-buttons button {
    background-color: #f8f9fa;
    border: 1px solid #f8f9fa;
    border-radius: 4px;
    color: #3c4043;
    font-size: 14px;
    margin: 11px 4px;
    padding: 0 16px;
    line-height: 27px;
    height: 36px;
    min-width: 54px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.search-buttons button:hover {
    border-color: #dadce0;
    box-shadow: 0 1px 1px rgba(0,0,0,.1);
}

#talkToAIButton {
    background-color: #e8f0fe;
    color: #1967d2;
    border-color: #e8f0fe;
}

#talkToAIButton:hover {
     background-color: #d2e3fc;
     border-color: #c6dafb;
}

#talkToAIButton .fas {
    margin-right: 6px;
}

#resultsHeader {
    background-color: #fff;
    border-bottom: 1px solid #dfe1e5;
    padding: 0 20px 0 15px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
    display: flex;
    align-items: center;
}

#resultsHeader .logo-container {
    margin-right: 15px;
    padding-left: 10px;
}

#resultsHeader .search-input-container {
    max-width: 690px;
    height: 40px;
    margin-left: 5px;
}

#resultsHeader .search-icon {
    padding-left: 5px;
}

#resultsHeader .search-input {
    font-size: 15px;
}

#searchTabsContainer {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: #fff;
    border-bottom: 1px solid #dfe1e5;
    padding: 0 20px 0 160px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 999;
}

body.ai-talk-active #searchTabsContainer {
    display: none;
}

body.ai-talk-active .main-content-centered {
     padding-top: 80px;
}

#searchTabs {
    display: flex;
    align-items: center;
}

.tab-item {
    padding: 10px 12px;
    margin-right: 8px;
    font-size: 13px;
    color: #5f6368;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
}

.tab-item i {
    margin-right: 6px;
    font-size: 14px;
}

.tab-item.active {
    color: #1a73e8;
    border-bottom-color: #1a73e8;
}

.tab-item:hover {
    color: #1a73e8;
}

#toolsButton {
    font-size: 13px;
    color: #5f6368;
    padding: 10px 12px;
    cursor: pointer;
    margin-left: auto;
}

#toolsButton:hover {
    color: #1a73e8;
}

#resultsStatsContainer {
    margin-bottom: 10px;
    padding-top: 10px;
}

#resultsStats {
    font-size: 13px;
    color: #70757a;
}

#aiSummarySection {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f4f9;
    border: 1px solid #d1e0f0;
    border-radius: 8px;
}

#aiSummarySection h3 {
    font-size: 16px;
    color: #1a73e8;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

#aiSummarySection h3 .fas {
    margin-right: 8px;
}

#aiSummaryContent {
    font-size: 14px;
    color: #3c4043;
    line-height: 1.6;
}

#aiTalkResponseSection {
    margin-top: 20px;
    width: 100%;
    max-width: 700px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
}

#aiTalkResponseSection h3 {
    font-size: 18px;
    color: #1a73e8;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

#aiTalkResponseSection h3 .fas {
    margin-right: 8px;
}

#aiTalkResponseContent {
    font-size: 15px;
    color: #3c4043;
    line-height: 1.7;
    white-space: pre-wrap;
}

.gemini-loading-spinner {
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1a73e8;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 0.8s linear infinite;
    margin: 5px auto;
}

.inline-gemini-loader {
    display: inline-block;
    vertical-align: middle;
    margin-left: 8px;
}

.results-area-container {
    width: 100%;
    max-width: 1200px;
    margin-left: 160px;
    text-align: left;
    display: flex;
    gap: 20px;
}

body:not(.results-active):not(.ai-talk-active) .results-area-container {
     margin-left: auto;
     margin-right: auto;
     display: block;
}

body.ai-talk-active .results-area-container {
    margin-left: auto;
    margin-right: auto;
    display: block;
}

.results-main-content {
    flex: 1;
    min-width: 0;
}

.results-sidebar {
    width: 350px;
    flex-shrink: 0;
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
    .results-area-container {
        flex-direction: column;
        margin-left: 20px;
        margin-right: 20px;
        max-width: none;
    }

    .results-sidebar {
        width: 100%;
        order: -1;
    }

    #searchTabsContainer {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .results-area-container {
        margin-left: 10px;
        margin-right: 10px;
    }

    #resultsHeader {
        padding: 0 10px;
    }

    #resultsHeader .logo-container {
        margin-right: 10px;
        padding-left: 5px;
    }

    #resultsHeader .search-input-container {
        margin-left: 0;
        max-width: none;
    }

    #searchTabsContainer {
        padding: 0 10px;
        overflow-x: auto;
        white-space: nowrap;
    }

    #searchTabs {
        display: inline-flex;
        min-width: max-content;
    }

    .tab-item {
        flex-shrink: 0;
    }
}

.results-container {
    width: 100%;
    text-align: left;
}

.result-item {
    margin-bottom: 15px;
}

.result-item a {
    text-decoration: none;
}

.result-item .url-line {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
}

.result-item .favicon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 2px;
    object-fit: contain;
}

.result-item .url {
    font-size: 14px;
    color: #202124;
    display: block;
}

.result-item .url .domain {
    font-weight: 400;
}

.result-item .url .path {
    color: #5f6368;
}

.result-item .title {
    font-size: 20px;
    color: #1a0dab;
    font-weight: 400;
    margin-bottom: 3px;
}

.result-item .title:hover {
    text-decoration: underline;
}

.result-item .snippet {
    font-size: 14px;
    color: #4d5156;
    line-height: 1.57;
    margin-bottom: 8px;
}



.image-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

.image-result-item {
    border: 1px solid #ebebeb;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.image-result-item a {
    display: block;
    text-decoration: none;
    color: #202124;
}

.image-result-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.image-result-item .image-title {
    font-size: 12px;
    padding: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

/* Video Results Styles */
.video-result-item {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.video-result-item:last-child {
    border-bottom: none;
}

.video-thumbnail-container {
    flex-shrink: 0;
    margin-right: 16px;
    position: relative;
}

.video-thumbnail {
    width: 160px;
    height: 90px;
    object-fit: cover;
    border-radius: 8px;
    background-color: #f0f0f0;
}

.video-duration {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 11px;
    font-weight: 500;
}

.video-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.video-content {
    flex: 1;
    min-width: 0;
}

.video-result-item .title {
    font-size: 18px;
    color: #1a0dab;
    font-weight: 400;
    margin-bottom: 4px;
    line-height: 1.3;
    display: block;
    text-decoration: none;
}

.video-result-item .title:hover {
    text-decoration: underline;
}

.video-result-item .url {
    font-size: 14px;
    color: #202124;
    margin-bottom: 4px;
}

.video-result-item .snippet {
    font-size: 14px;
    color: #4d5156;
    line-height: 1.57;
}

.video-meta {
    font-size: 13px;
    color: #70757a;
    margin-bottom: 4px;
}

@media (max-width: 768px) {
    .video-result-item {
        flex-direction: column;
    }

    .video-thumbnail-container {
        margin-right: 0;
        margin-bottom: 12px;
        align-self: flex-start;
    }

    .video-thumbnail {
        width: 100%;
        max-width: 300px;
        height: auto;
        aspect-ratio: 16/9;
    }
}

#paaContainer {
    margin-top: 30px;
    margin-bottom: 20px;
}

#paaContainer.sidebar {
    margin-top: 0;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    padding: 16px;
}

#relatedSearchesSidebar {
    margin-top: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    padding: 16px;
}

#relatedSearchesSidebar h3 {
    font-size: 16px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

#relatedSearchesSidebar h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 14px;
}

#relatedSearchesSidebar .related-search-item {
    display: block;
    width: 100%;
    margin-bottom: 8px;
    margin-right: 0;
    text-align: left;
    padding: 8px 12px;
    border-radius: 8px;
}

#paaContainer h3 {
    font-size: 18px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#paaContainer.sidebar h3 {
    font-size: 16px;
    margin-bottom: 12px;
}

#paaContainer h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 16px;
}

.paa-item {
    border-bottom: 1px solid #ebebeb;
}

.paa-item:last-child {
    border-bottom: none;
}

.paa-question {
    padding: 12px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #202124;
}

.paa-question:hover {
    color: #1a0dab;
}

.paa-question .toggle-icon {
    transition: transform 0.2s ease-in-out;
    font-size: 14px;
    color: #5f6368;
}

.paa-item.open .paa-question .toggle-icon {
    transform: rotate(180deg);
}

.paa-answer {
    padding: 0 0 12px 0;
    font-size: 14px;
    color: #4d5156;
    line-height: 1.6;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.paa-item.open .paa-answer {
    max-height: 500px;
    padding-bottom: 12px;
}

#relatedSearchesContainer {
    margin-top: 40px;
    margin-bottom: 30px;
    border-top: 1px solid #ebebeb;
    padding-top: 20px;
}

#relatedSearchesContainer h3 {
    font-size: 18px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#relatedSearchesContainer h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 16px;
}

.related-search-item {
    display: inline-block;
    background-color: #f1f3f4;
    border-radius: 16px;
    padding: 8px 16px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #3c4043;
    cursor: pointer;
}

.related-search-item:hover {
    background-color: #e8eaed;
}

.footer {
    background-color: #f2f2f2;
    padding: 15px 30px;
    border-top: 1px solid #e4e4e4;
    font-size: 14px;
    color: #70757a;
    text-align: center;
    width: 100%;
}

.message-area {
    margin-top: 20px;
    color: #70757a;
    width: 100%;
}

#paginationContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 40px;
}

.pagination-nav-button {
    display: flex;
    align-items: center;
    color: #1a73e8;
    text-decoration: none;
    margin: 0 20px;
}

.pagination-nav-button:hover {
    text-decoration: underline;
}

.pagination-nav-button.disabled {
    color: #70757a;
    cursor: default;
    text-decoration: none;
}

.pagination-nav-button i {
    font-size: 20px;
}

.pagination-nav-button .nav-text {
    margin-left: 8px;
    font-size: 14px;
}

.page-numbers-container {
    display: flex;
    align-items: center;
}

.page-number {
    font-family: 'Arial', sans-serif;
    font-size: 13px;
    color: #1a73e8;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border-radius: 2px;
}

.page-number.current {
    background-color: #1a73e8;
    color: #fff;
    font-weight: bold;
}

.page-number:not(.current):hover {
    text-decoration: underline;
}

.page-number.letter-g { color: #4285F4; }
.page-number.letter-o1 { color: #EA4335; }
.page-number.letter-o2 { color: #FBBC05; }
.page-number.letter-o3 { color: #FBBC05; }
.page-number.letter-g2 { color: #4285F4; }
.page-number.letter-l { color: #34A853; }
.page-number.letter-e { color: #EA4335; }

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

/* Additional Mobile Responsive Styles */
@media (max-width: 640px) {
    .main-content-centered {
        padding: 10px;
    }

    body.results-active .main-content-centered,
    body.ai-talk-active .main-content-centered {
        padding-top: 110px;
    }

    body.ai-talk-active .main-content-centered {
        padding-top: 70px;
    }

    .search-input-container {
        height: 42px;
        padding: 0 12px;
    }

    .search-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .search-buttons {
        flex-direction: column;
        gap: 8px;
        align-items: center;
    }

    .search-buttons button {
        width: 200px;
        margin: 4px 0;
    }

    #resultsHeader {
        height: 56px;
        padding: 0 8px;
    }

    #resultsHeader .search-input-container {
        height: 36px;
    }

    #searchTabsContainer {
        height: 40px;
        top: 56px;
        padding: 0 8px;
    }

    .tab-item {
        padding: 8px 10px;
        font-size: 12px;
        margin-right: 4px;
    }

    .tab-item i {
        font-size: 12px;
        margin-right: 4px;
    }

    .result-item {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
    }

    .result-item .title {
        font-size: 18px;
        line-height: 1.3;
    }

    .result-item .snippet {
        font-size: 14px;
        line-height: 1.5;
    }

    .image-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
    }

    .image-result-item img {
        height: 100px;
    }

    .pagination-nav-button {
        margin: 0 10px;
    }

    .pagination-nav-button .nav-text {
        display: none;
    }

    .page-number {
        padding: 8px 12px;
        margin: 0 1px;
    }

    #aiSummarySection,
    #aiTalkResponseSection {
        padding: 12px;
        margin-bottom: 16px;
    }

    #aiSummarySection h3,
    #aiTalkResponseSection h3 {
        font-size: 16px;
    }

    .paa-question {
        font-size: 15px;
        padding: 10px 0;
    }

    .related-search-item {
        padding: 6px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        font-size: 13px;
    }

    .footer {
        padding: 12px 16px;
        font-size: 13px;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .search-buttons button {
        width: 160px;
        font-size: 13px;
        height: 32px;
        line-height: 20px;
    }

    .result-item .title {
        font-size: 16px;
    }

    .result-item .snippet {
        font-size: 13px;
    }

    .image-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .image-result-item img {
        height: 80px;
    }

    .page-number {
        padding: 6px 8px;
        font-size: 12px;
    }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: arial, sans-serif;
    font-size: 14px;
    color: #202124;
    background-color: #fff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    height: 60px;
}

.header-left,
.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-link {
    color: #000;
    text-decoration: none;
    font-size: 13px;
    padding: 5px 10px;
    border-radius: 2px;
    transition: background-color 0.3s;
}

.header-link:hover {
    text-decoration: underline;
}

.apps-menu {
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.apps-menu:hover {
    background-color: rgba(60, 64, 67, 0.08);
}

.apps-icon {
    width: 24px;
    height: 24px;
    fill: #5f6368;
}

.profile-pic {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
}

.profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main Content */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin-top: -60px;
}

.logo {
    margin-bottom: 30px;
}

.google-logo {
    height: 92px;
    width: 272px;
}

/* Search Container */
.search-container {
    width: 100%;
    max-width: 584px;
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    align-items: center;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    height: 44px;
    padding: 0 8px 0 16px;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 2px 5px 1px rgba(64, 60, 67, 0.16);
    transition: box-shadow 0.3s;
}

.search-box:hover {
    box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
}

.search-box:focus-within {
    box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
    border-color: transparent;
}

.search-icon {
    margin-right: 13px;
    margin-top: 3px;
}

.search-icon svg {
    width: 20px;
    height: 20px;
    fill: #9aa0a6;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    color: #202124;
    background: transparent;
    height: 34px;
}

.voice-search,
.camera-search {
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.voice-search:hover,
.camera-search:hover {
    background-color: rgba(60, 64, 67, 0.08);
}

.voice-icon,
.camera-icon {
    width: 24px;
    height: 24px;
}

/* Buttons */
.buttons {
    display: flex;
    gap: 14px;
    margin-bottom: 30px;
}

.btn {
    background-color: #f8f9fa;
    border: 1px solid #f8f9fa;
    border-radius: 4px;
    color: #3c4043;
    font-family: arial, sans-serif;
    font-size: 14px;
    margin: 11px 4px;
    padding: 0 20px;
    line-height: 27px;
    height: 36px;
    min-width: 54px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.1s;
}

.btn:hover {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    background-color: #f1f3f4;
    border: 1px solid #dadce0;
    color: #202124;
}

.btn:focus {
    border: 1px solid #4285f4;
    outline: none;
}

/* Languages */
.languages {
    font-size: 13px;
    color: #3c4043;
}

.languages a {
    color: #1a0dab;
    text-decoration: none;
    margin: 0 3px;
}

.languages a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background-color: #f2f2f2;
    border-top: 1px solid #dadce0;
    margin-top: auto;
}

.footer-country {
    padding: 15px 30px;
    border-bottom: 1px solid #dadce0;
    color: #70757a;
    font-size: 15px;
}

.footer-links {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    flex-wrap: wrap;
}

.footer-left,
.footer-right {
    display: flex;
    gap: 30px;
    padding: 15px;
}

.footer-links a {
    color: #70757a;
    text-decoration: none;
    font-size: 14px;
}

.footer-links a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 6px 20px;
    }
    
    .main {
        padding: 20px;
        margin-top: -40px;
    }
    
    .google-logo {
        height: 56px;
        width: 166px;
    }
    
    .search-container {
        max-width: 90%;
    }
    
    .buttons {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .footer-links {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-left,
    .footer-right {
        justify-content: center;
        gap: 20px;
        padding: 10px;
    }
    
    .languages {
        text-align: center;
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .header-left,
    .header-right {
        gap: 10px;
    }
    
    .header-link {
        font-size: 12px;
        padding: 3px 6px;
    }
    
    .search-box {
        height: 40px;
    }
    
    .search-input {
        font-size: 14px;
    }
    
    .btn {
        font-size: 13px;
        padding: 0 16px;
        height: 32px;
        line-height: 24px;
    }
}

body {
    font-family: 'Inter', sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #fff;
    color: #202124;
    margin: 0;
}

.main-content-centered {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

body.results-active .main-content-centered,
body.ai-talk-active .main-content-centered {
    padding-top: 125px;
    justify-content: flex-start;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 584px;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    height: 46px;
    padding: 0 15px;
    background-color: #fff;
}

.search-input-container:hover,
.search-input-container:focus-within {
    border-color: #dfe1e5;
    box-shadow: 0 1px 6px rgba(32,33,36,.28);
}

.search-icon {
    color: #9aa0a6;
    margin-right: 10px;
}

.search-input {
    flex-grow: 1;
    border: none;
    outline: none;
    font-size: 16px;
    background-color: transparent;
}

.search-buttons button {
    background-color: #f8f9fa;
    border: 1px solid #f8f9fa;
    border-radius: 4px;
    color: #3c4043;
    font-size: 14px;
    margin: 11px 4px;
    padding: 0 16px;
    line-height: 27px;
    height: 36px;
    min-width: 54px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.search-buttons button:hover {
    border-color: #dadce0;
    box-shadow: 0 1px 1px rgba(0,0,0,.1);
}

#talkToAIButton {
    background-color: #e8f0fe;
    color: #1967d2;
    border-color: #e8f0fe;
}

#talkToAIButton:hover {
     background-color: #d2e3fc;
     border-color: #c6dafb;
}

#talkToAIButton .fas {
    margin-right: 6px;
}

#resultsHeader {
    background-color: #fff;
    border-bottom: 1px solid #dfe1e5;
    padding: 0 20px 0 15px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
    display: flex;
    align-items: center;
}

#resultsHeader .logo-container {
    margin-right: 15px;
    padding-left: 10px;
}

#resultsHeader .search-input-container {
    max-width: 690px;
    height: 40px;
    margin-left: 5px;
}

#resultsHeader .search-icon {
    padding-left: 5px;
}

#resultsHeader .search-input {
    font-size: 15px;
}

#searchTabsContainer {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: #fff;
    border-bottom: 1px solid #dfe1e5;
    padding: 0 20px 0 160px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 999;
}

body.ai-talk-active #searchTabsContainer {
    display: none;
}

body.ai-talk-active .main-content-centered {
     padding-top: 80px;
}

#searchTabs {
    display: flex;
    align-items: center;
}

.tab-item {
    padding: 10px 12px;
    margin-right: 8px;
    font-size: 13px;
    color: #5f6368;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
}

.tab-item i {
    margin-right: 6px;
    font-size: 14px;
}

.tab-item.active {
    color: #1a73e8;
    border-bottom-color: #1a73e8;
}

.tab-item:hover {
    color: #1a73e8;
}

#toolsButton {
    font-size: 13px;
    color: #5f6368;
    padding: 10px 12px;
    cursor: pointer;
    margin-left: auto;
}

#toolsButton:hover {
    color: #1a73e8;
}

#resultsStatsContainer {
    margin-bottom: 10px;
    padding-top: 10px;
}

#resultsStats {
    font-size: 13px;
    color: #70757a;
}

#aiSummarySection {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f4f9;
    border: 1px solid #d1e0f0;
    border-radius: 8px;
}

#aiSummarySection h3 {
    font-size: 16px;
    color: #1a73e8;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

#aiSummarySection h3 .fas {
    margin-right: 8px;
}

#aiSummaryContent {
    font-size: 14px;
    color: #3c4043;
    line-height: 1.6;
}

#aiTalkResponseSection {
    margin-top: 20px;
    width: 100%;
    max-width: 700px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
}

#aiTalkResponseSection h3 {
    font-size: 18px;
    color: #1a73e8;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

#aiTalkResponseSection h3 .fas {
    margin-right: 8px;
}

#aiTalkResponseContent {
    font-size: 15px;
    color: #3c4043;
    line-height: 1.7;
    white-space: pre-wrap;
}

.gemini-loading-spinner {
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1a73e8;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 0.8s linear infinite;
    margin: 5px auto;
}

.inline-gemini-loader {
    display: inline-block;
    vertical-align: middle;
    margin-left: 8px;
}

.results-area-container {
    width: 100%;
    max-width: 600px;
    margin-left: 160px;
    text-align: left;
}

body:not(.results-active):not(.ai-talk-active) .results-area-container {
     margin-left: auto;
     margin-right: auto;
}

body.ai-talk-active .results-area-container {
    margin-left: auto;
    margin-right: auto;
}

.results-container {
    width: 100%;
    text-align: left;
}

.result-item {
    margin-bottom: 15px;
}

.result-item a {
    text-decoration: none;
}

.result-item .url-line {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
}

.result-item .favicon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 2px;
    object-fit: contain;
}

.result-item .url {
    font-size: 14px;
    color: #202124;
    display: block;
}

.result-item .url .domain {
    font-weight: 400;
}

.result-item .url .path {
    color: #5f6368;
}

.result-item .title {
    font-size: 20px;
    color: #1a0dab;
    font-weight: 400;
    margin-bottom: 3px;
}

.result-item .title:hover {
    text-decoration: underline;
}

.result-item .snippet {
    font-size: 14px;
    color: #4d5156;
    line-height: 1.57;
    margin-bottom: 8px;
}

/* ELI5 Styles */
.eli5-button {
    background-color: #e8f0fe;
    color: #1967d2;
    border: 1px solid #d1e0f0;
    border-radius: 12px;
    padding: 3px 8px;
    font-size: 11px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-top: 5px;
}

.eli5-button:hover {
    background-color: #d2e3fc;
}

.eli5-button .fas {
    margin-right: 4px;
    font-size: 10px;
}

.eli5-content-wrapper {
    margin-top: 8px;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.5;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out, margin-top 0.3s ease-out;
}

.eli5-content-wrapper.open {
    max-height: 300px;
    padding: 10px;
    margin-top: 8px;
}

.image-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

.image-result-item {
    border: 1px solid #ebebeb;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.image-result-item a {
    display: block;
    text-decoration: none;
    color: #202124;
}

.image-result-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.image-result-item .image-title {
    font-size: 12px;
    padding: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

#paaContainer {
    margin-top: 30px;
    margin-bottom: 20px;
}

#paaContainer h3 {
    font-size: 18px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#paaContainer h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 16px;
}

.paa-item {
    border-bottom: 1px solid #ebebeb;
}

.paa-item:last-child {
    border-bottom: none;
}

.paa-question {
    padding: 12px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #202124;
}

.paa-question:hover {
    color: #1a0dab;
}

.paa-question .toggle-icon {
    transition: transform 0.2s ease-in-out;
    font-size: 14px;
    color: #5f6368;
}

.paa-item.open .paa-question .toggle-icon {
    transform: rotate(180deg);
}

.paa-answer {
    padding: 0 0 12px 0;
    font-size: 14px;
    color: #4d5156;
    line-height: 1.6;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.paa-item.open .paa-answer {
    max-height: 500px;
    padding-bottom: 12px;
}

#relatedSearchesContainer {
    margin-top: 40px;
    margin-bottom: 30px;
    border-top: 1px solid #ebebeb;
    padding-top: 20px;
}

#relatedSearchesContainer h3 {
    font-size: 18px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#relatedSearchesContainer h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 16px;
}

.related-search-item {
    display: inline-block;
    background-color: #f1f3f4;
    border-radius: 16px;
    padding: 8px 16px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #3c4043;
    cursor: pointer;
}

.related-search-item:hover {
    background-color: #e8eaed;
}

.footer {
    background-color: #f2f2f2;
    padding: 15px 30px;
    border-top: 1px solid #e4e4e4;
    font-size: 14px;
    color: #70757a;
    text-align: center;
    width: 100%;
}

.message-area {
    margin-top: 20px;
    color: #70757a;
    width: 100%;
}

#paginationContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 40px;
}

.pagination-nav-button {
    display: flex;
    align-items: center;
    color: #1a73e8;
    text-decoration: none;
    margin: 0 20px;
}

.pagination-nav-button:hover {
    text-decoration: underline;
}

.pagination-nav-button.disabled {
    color: #70757a;
    cursor: default;
    text-decoration: none;
}

.pagination-nav-button i {
    font-size: 20px;
}

.pagination-nav-button .nav-text {
    margin-left: 8px;
    font-size: 14px;
}

.page-numbers-container {
    display: flex;
    align-items: center;
}

.page-number {
    font-family: 'Arial', sans-serif;
    font-size: 13px;
    color: #1a73e8;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border-radius: 2px;
}

.page-number.current {
    background-color: #1a73e8;
    color: #fff;
    font-weight: bold;
}

.page-number:not(.current):hover {
    text-decoration: underline;
}

.page-number.letter-g { color: #4285F4; }
.page-number.letter-o1 { color: #EA4335; }
.page-number.letter-o2 { color: #FBBC05; }
.page-number.letter-o3 { color: #FBBC05; }
.page-number.letter-g2 { color: #4285F4; }
.page-number.letter-l { color: #34A853; }
.page-number.letter-e { color: #EA4335; }

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

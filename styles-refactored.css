/**
 * Google Clone Styles - Refactored
 * Organized by component and responsive design
 */

/* ===== BASE STYLES ===== */
body {
    font-family: 'Inter', sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #fff;
    color: #202124;
    margin: 0;
}

.hidden {
    display: none !important;
}

/* ===== LAYOUT CONTAINERS ===== */
.main-content-centered {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

body.results-active .main-content-centered,
body.ai-talk-active .main-content-centered {
    padding-top: 125px;
    justify-content: flex-start;
}

body.ai-talk-active .main-content-centered {
    padding-top: 80px;
}

/* ===== SEARCH INPUT COMPONENTS ===== */
.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 584px;
    border: 1px solid #dfe1e5;
    border-radius: 24px;
    height: 46px;
    padding: 0 15px;
    background-color: #fff;
}

.search-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
}

.voice-search-btn,
.camera-search-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.2s;
}

.voice-search-btn:hover,
.camera-search-btn:hover {
    background-color: rgba(60, 64, 67, 0.08);
}

.voice-search-btn.listening {
    color: #ea4335;
    background-color: rgba(234, 67, 53, 0.1);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dfe1e5;
    border-top: none;
    border-radius: 0 0 24px 24px;
    box-shadow: 0 4px 6px rgba(32, 33, 36, 0.28);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #202124;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item .suggestion-icon {
    margin-right: 12px;
    color: #70757a;
    width: 16px;
}

.suggestion-item .suggestion-text {
    flex: 1;
}

.suggestion-item .suggestion-type {
    font-size: 12px;
    color: #70757a;
    margin-left: 8px;
}

.search-input-container:hover,
.search-input-container:focus-within {
    border-color: #dfe1e5;
    box-shadow: 0 1px 6px rgba(32, 33, 36, .28);
}

.search-icon {
    color: #9aa0a6;
    margin-right: 10px;
}

.search-input {
    flex-grow: 1;
    border: none;
    outline: none;
    font-size: 16px;
    background-color: transparent;
}

.search-buttons button {
    background-color: #f8f9fa;
    border: 1px solid #f8f9fa;
    border-radius: 4px;
    color: #3c4043;
    font-size: 14px;
    margin: 11px 4px;
    padding: 0 16px;
    line-height: 27px;
    height: 36px;
    min-width: 54px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.search-buttons button:hover {
    border-color: #dadce0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
}

#talkToAIButton {
    background-color: #e8f0fe;
    color: #1967d2;
    border-color: #e8f0fe;
}

#talkToAIButton:hover {
    background-color: #d2e3fc;
    border-color: #c6dafb;
}

#talkToAIButton .fas {
    margin-right: 6px;
}

/* ===== HEADER COMPONENTS ===== */
#resultsHeader {
    background-color: #fff;
    border-bottom: 1px solid #dfe1e5;
    padding: 0 20px 0 15px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
    display: flex;
    align-items: center;
}

#resultsHeader .logo-container {
    margin-right: 15px;
    padding-left: 10px;
}

#resultsHeader .search-input-container {
    max-width: 690px;
    height: 40px;
    margin-left: 5px;
}

#resultsHeader .search-icon {
    padding-left: 5px;
}

#resultsHeader .search-input {
    font-size: 15px;
}

/* ===== SEARCH TABS ===== */
#searchTabsContainer {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: #fff;
    border-bottom: 1px solid #dfe1e5;
    padding: 0 20px 0 160px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 999;
}

body.ai-talk-active #searchTabsContainer {
    display: none;
}

#searchTabs {
    display: flex;
    align-items: center;
}

.tab-item {
    padding: 10px 12px;
    margin-right: 8px;
    font-size: 13px;
    color: #5f6368;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
}

.tab-item i {
    margin-right: 6px;
    font-size: 14px;
}

.tab-item.active {
    color: #1a73e8;
    border-bottom-color: #1a73e8;
}

.tab-item:hover {
    color: #1a73e8;
}

#toolsButton {
    font-size: 13px;
    color: #5f6368;
    padding: 10px 12px;
    cursor: pointer;
    margin-left: auto;
}

#toolsButton:hover {
    color: #1a73e8;
}

/* Search Filters */
.search-filters {
    background-color: #f8f9fa;
    border-top: 1px solid #dadce0;
    padding: 12px 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-size: 13px;
    color: #70757a;
    font-weight: 500;
}

.filter-group select {
    border: 1px solid #dadce0;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 13px;
    background-color: white;
    color: #202124;
}

.filter-group select:focus {
    outline: none;
    border-color: #1a73e8;
}

/* Quick Access */
.quick-access {
    margin-top: 30px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 584px;
    width: 100%;
}

.trending-searches,
.search-history {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
}

.trending-searches h3,
.search-history h3 {
    font-size: 16px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.trending-list,
.history-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.trending-item,
.history-item {
    padding: 8px 12px;
    background-color: white;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: #202124;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.trending-item:hover,
.history-item:hover {
    background-color: #e8f0fe;
}

.trending-item .trend-icon,
.history-item .history-icon {
    color: #70757a;
    font-size: 12px;
}

.history-item .delete-history {
    color: #70757a;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.history-item .delete-history:hover {
    background-color: rgba(60, 64, 67, 0.08);
}

/* ===== RESULTS LAYOUT ===== */
.results-area-container {
    width: 100%;
    max-width: 1200px;
    margin-left: 160px;
    text-align: left;
    display: flex;
    gap: 20px;
}

body:not(.results-active):not(.ai-talk-active) .results-area-container {
    margin-left: auto;
    margin-right: auto;
    display: block;
}

body.ai-talk-active .results-area-container {
    margin-left: auto;
    margin-right: auto;
    display: block;
}

.results-main-content {
    flex: 1;
    min-width: 0;
}

.results-sidebar {
    width: 350px;
    flex-shrink: 0;
}

#resultsStatsContainer {
    margin-bottom: 10px;
    padding-top: 10px;
}

#resultsStats {
    font-size: 13px;
    color: #70757a;
}

/* ===== RESULTS COMPONENTS ===== */
.results-container {
    width: 100%;
    text-align: left;
}

.result-item {
    margin-bottom: 15px;
}

.result-item a {
    text-decoration: none;
}

.result-item .url-line {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
}

.result-item .favicon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 2px;
    object-fit: contain;
}

.result-item .url {
    font-size: 14px;
    color: #202124;
    display: block;
}

.result-item .url .domain {
    font-weight: 400;
}

.result-item .url .path {
    color: #5f6368;
}

.result-item .title {
    font-size: 20px;
    color: #1a0dab;
    font-weight: 400;
    margin-bottom: 3px;
}

.result-item .title:hover {
    text-decoration: underline;
}

.result-item .snippet {
    font-size: 14px;
    color: #4d5156;
    line-height: 1.57;
    margin-bottom: 8px;
}

/* ===== IMAGE RESULTS ===== */
.image-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

.image-result-item {
    border: 1px solid #ebebeb;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.image-result-item a {
    display: block;
    text-decoration: none;
    color: #202124;
}

.image-result-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.image-result-item .image-title {
    font-size: 12px;
    padding: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

/* ===== VIDEO RESULTS ===== */
.video-result-item {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.video-result-item:last-child {
    border-bottom: none;
}

.video-thumbnail-container {
    flex-shrink: 0;
    margin-right: 16px;
    position: relative;
}

.video-thumbnail {
    width: 160px;
    height: 90px;
    object-fit: cover;
    border-radius: 8px;
    background-color: #f0f0f0;
}

.video-duration {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 11px;
    font-weight: 500;
}

.video-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.video-content {
    flex: 1;
    min-width: 0;
}

.video-result-item .title {
    font-size: 18px;
    color: #1a0dab;
    font-weight: 400;
    margin-bottom: 4px;
    line-height: 1.3;
    display: block;
    text-decoration: none;
}

.video-result-item .title:hover {
    text-decoration: underline;
}

.video-result-item .url {
    font-size: 14px;
    color: #202124;
    margin-bottom: 4px;
}

.video-result-item .snippet {
    font-size: 14px;
    color: #4d5156;
    line-height: 1.57;
}

.video-meta {
    font-size: 13px;
    color: #70757a;
    margin-bottom: 4px;
}

/* ===== AI COMPONENTS ===== */
#aiSummarySection {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f4f9;
    border: 1px solid #d1e0f0;
    border-radius: 8px;
}

#aiSummarySection h3 {
    font-size: 16px;
    color: #1a73e8;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

#aiSummarySection h3 .fas {
    margin-right: 8px;
}

#aiSummaryContent {
    font-size: 14px;
    color: #3c4043;
    line-height: 1.6;
}

#aiTalkResponseSection {
    margin-top: 20px;
    width: 100%;
    max-width: 700px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
}

#aiTalkResponseSection h3 {
    font-size: 18px;
    color: #1a73e8;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

#aiTalkResponseSection h3 .fas {
    margin-right: 8px;
}

#aiTalkResponseContent {
    font-size: 15px;
    color: #3c4043;
    line-height: 1.7;
    white-space: pre-wrap;
}

/* ===== SIDEBAR COMPONENTS ===== */
#paaContainer {
    margin-top: 30px;
    margin-bottom: 20px;
}

#paaContainer.sidebar {
    margin-top: 0;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    padding: 16px;
}

#paaContainer h3 {
    font-size: 18px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#paaContainer.sidebar h3 {
    font-size: 16px;
    margin-bottom: 12px;
}

#paaContainer h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 16px;
}

.paa-item {
    border-bottom: 1px solid #ebebeb;
}

.paa-item:last-child {
    border-bottom: none;
}

.paa-question {
    padding: 12px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #202124;
}

.paa-question:hover {
    color: #1a0dab;
}

.paa-question .toggle-icon {
    transition: transform 0.2s ease-in-out;
    font-size: 14px;
    color: #5f6368;
}

.paa-item.open .paa-question .toggle-icon {
    transform: rotate(180deg);
}

.paa-answer {
    padding: 0 0 12px 0;
    font-size: 14px;
    color: #4d5156;
    line-height: 1.6;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.paa-item.open .paa-answer {
    max-height: 500px;
    padding-bottom: 12px;
}

/* ===== RELATED SEARCHES ===== */
#relatedSearchesContainer {
    margin-top: 40px;
    margin-bottom: 30px;
    border-top: 1px solid #ebebeb;
    padding-top: 20px;
}

#relatedSearchesContainer h3 {
    font-size: 18px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

#relatedSearchesContainer h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 16px;
}

.related-search-item {
    display: inline-block;
    background-color: #f1f3f4;
    border-radius: 16px;
    padding: 8px 16px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #3c4043;
    cursor: pointer;
}

.related-search-item:hover {
    background-color: #e8eaed;
}

#relatedSearchesSidebar {
    margin-top: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    padding: 16px;
}

#relatedSearchesSidebar h3 {
    font-size: 16px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

#relatedSearchesSidebar h3 .fas {
    color: #1a73e8;
    margin-right: 8px;
    font-size: 14px;
}

#relatedSearchesSidebar .related-search-item {
    display: block;
    width: 100%;
    margin-bottom: 8px;
    margin-right: 0;
    text-align: left;
    padding: 8px 12px;
    border-radius: 8px;
}

/* Knowledge Panel */
#knowledgePanel {
    margin-bottom: 20px;
}

.knowledge-card {
    background-color: #f8f9fa;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    padding: 16px;
}

.knowledge-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.knowledge-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 12px;
}

.knowledge-title {
    font-size: 18px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 4px;
}

.knowledge-subtitle {
    font-size: 14px;
    color: #70757a;
}

.knowledge-facts {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.knowledge-fact {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.fact-label {
    color: #70757a;
    font-weight: 500;
}

.fact-value {
    color: #202124;
    text-align: right;
}

/* Calculator Widget */
#calculatorWidget {
    margin-bottom: 20px;
}

#calculatorInput {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    font-size: 16px;
    margin-bottom: 8px;
}

#calculatorResult {
    background-color: #e8f0fe;
    padding: 12px;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 500;
    color: #1a73e8;
    text-align: center;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Weather Widget */
#weatherWidget {
    margin-bottom: 20px;
}

.weather-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background-color: #e3f2fd;
    border-radius: 8px;
}

.weather-icon {
    font-size: 32px;
    color: #1976d2;
}

.weather-info {
    flex: 1;
}

.weather-temp {
    font-size: 24px;
    font-weight: 500;
    color: #202124;
}

.weather-desc {
    font-size: 14px;
    color: #70757a;
}

.weather-location {
    font-size: 12px;
    color: #70757a;
    margin-top: 4px;
}

/* ===== PAGINATION ===== */
#paginationContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 40px;
}

.pagination-nav-button {
    display: flex;
    align-items: center;
    color: #1a73e8;
    text-decoration: none;
    margin: 0 20px;
}

.pagination-nav-button:hover {
    text-decoration: underline;
}

.pagination-nav-button.disabled {
    color: #70757a;
    cursor: default;
    text-decoration: none;
}

.pagination-nav-button i {
    font-size: 20px;
}

.pagination-nav-button .nav-text {
    margin-left: 8px;
    font-size: 14px;
}

.page-numbers-container {
    display: flex;
    align-items: center;
}

.page-number {
    font-family: 'Arial', sans-serif;
    font-size: 13px;
    color: #1a73e8;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border-radius: 2px;
}

.page-number.current {
    background-color: #1a73e8;
    color: #fff;
    font-weight: bold;
}

.page-number:not(.current):hover {
    text-decoration: underline;
}

.page-number.letter-g { color: #4285F4; }
.page-number.letter-o1 { color: #EA4335; }
.page-number.letter-o2 { color: #FBBC05; }
.page-number.letter-o3 { color: #FBBC05; }
.page-number.letter-g2 { color: #4285F4; }
.page-number.letter-l { color: #34A853; }
.page-number.letter-e { color: #EA4335; }

/* ===== LOADING & UTILITY ===== */
.gemini-loading-spinner {
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1a73e8;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 0.8s linear infinite;
    margin: 5px auto;
}

.inline-gemini-loader {
    display: inline-block;
    vertical-align: middle;
    margin-left: 8px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message-area {
    margin-top: 20px;
    color: #70757a;
    width: 100%;
}

.footer {
    background-color: #f2f2f2;
    padding: 15px 30px;
    border-top: 1px solid #e4e4e4;
    font-size: 14px;
    color: #70757a;
    text-align: center;
    width: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .results-area-container {
        flex-direction: column;
        margin-left: 20px;
        margin-right: 20px;
        max-width: none;
    }

    .results-sidebar {
        width: 100%;
        order: -1;
    }

    #searchTabsContainer {
        padding: 0 20px;
    }

    .quick-access {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .search-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .results-area-container {
        margin-left: 10px;
        margin-right: 10px;
    }

    #resultsHeader {
        padding: 0 10px;
    }

    #resultsHeader .logo-container {
        margin-right: 10px;
        padding-left: 5px;
    }

    #resultsHeader .search-input-container {
        margin-left: 0;
        max-width: none;
    }

    #searchTabsContainer {
        padding: 0 10px;
        overflow-x: auto;
        white-space: nowrap;
    }

    #searchTabs {
        display: inline-flex;
        min-width: max-content;
    }

    .tab-item {
        flex-shrink: 0;
    }

    .video-result-item {
        flex-direction: column;
    }

    .video-thumbnail-container {
        margin-right: 0;
        margin-bottom: 12px;
        align-self: flex-start;
    }

    .video-thumbnail {
        width: 100%;
        max-width: 300px;
        height: auto;
        aspect-ratio: 16/9;
    }
}

@media (max-width: 640px) {
    .main-content-centered {
        padding: 10px;
    }

    body.results-active .main-content-centered,
    body.ai-talk-active .main-content-centered {
        padding-top: 110px;
    }

    body.ai-talk-active .main-content-centered {
        padding-top: 70px;
    }

    .search-input-container {
        height: 42px;
        padding: 0 12px;
    }

    .search-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .search-buttons {
        flex-direction: column;
        gap: 8px;
        align-items: center;
    }

    .search-buttons button {
        width: 200px;
        margin: 4px 0;
    }

    #resultsHeader {
        height: 56px;
        padding: 0 8px;
    }

    #resultsHeader .search-input-container {
        height: 36px;
    }

    #searchTabsContainer {
        height: 40px;
        top: 56px;
        padding: 0 8px;
    }

    .tab-item {
        padding: 8px 10px;
        font-size: 12px;
        margin-right: 4px;
    }

    .tab-item i {
        font-size: 12px;
        margin-right: 4px;
    }

    .result-item {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
    }

    .result-item .title {
        font-size: 18px;
        line-height: 1.3;
    }

    .result-item .snippet {
        font-size: 14px;
        line-height: 1.5;
    }

    .image-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
    }

    .image-result-item img {
        height: 100px;
    }

    .pagination-nav-button {
        margin: 0 10px;
    }

    .pagination-nav-button .nav-text {
        display: none;
    }

    .page-number {
        padding: 8px 12px;
        margin: 0 1px;
    }

    #aiSummarySection,
    #aiTalkResponseSection {
        padding: 12px;
        margin-bottom: 16px;
    }

    #aiSummarySection h3,
    #aiTalkResponseSection h3 {
        font-size: 16px;
    }

    .paa-question {
        font-size: 15px;
        padding: 10px 0;
    }

    .related-search-item {
        padding: 6px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        font-size: 13px;
    }

    .footer {
        padding: 12px 16px;
        font-size: 13px;
    }

    .search-actions {
        gap: 4px;
    }

    .voice-search-btn,
    .camera-search-btn {
        padding: 6px;
    }

    .search-suggestions {
        border-radius: 0 0 16px 16px;
    }

    .suggestion-item {
        padding: 12px 16px;
        font-size: 16px;
    }

    .quick-access {
        margin-top: 20px;
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .trending-searches,
    .search-history {
        padding: 12px;
    }

    .trending-item,
    .history-item {
        padding: 10px 12px;
        font-size: 15px;
    }

    .search-filters {
        padding: 8px 16px;
        gap: 8px;
    }

    .filter-group {
        width: 100%;
        justify-content: space-between;
    }

    .filter-group select {
        flex: 1;
        margin-left: 8px;
    }

    .knowledge-card {
        padding: 12px;
    }

    .knowledge-title {
        font-size: 16px;
    }

    .knowledge-fact {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .fact-value {
        text-align: left;
    }

    #calculatorInput {
        font-size: 16px;
        padding: 10px 12px;
    }

    #calculatorResult {
        font-size: 16px;
        padding: 10px;
    }

    .weather-card {
        padding: 10px;
    }

    .weather-temp {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .search-buttons button {
        width: 160px;
        font-size: 13px;
        height: 32px;
        line-height: 20px;
    }

    .result-item .title {
        font-size: 16px;
    }

    .result-item .snippet {
        font-size: 13px;
    }

    .image-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .image-result-item img {
        height: 80px;
    }

    .page-number {
        padding: 6px 8px;
        font-size: 12px;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Configuration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-key {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover { background: #3367d6; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 API Configuration Test</h1>
        <p>This page tests the API configuration for the Google Clone application.</p>
        
        <h2>📋 Current Configuration</h2>
        <div>
            <strong>Google Search API Key:</strong>
            <div class="api-key" id="googleApiKey">Loading...</div>
        </div>
        <div>
            <strong>Google Custom Search CX:</strong>
            <div class="api-key" id="googleCx">Loading...</div>
        </div>
        <div>
            <strong>Gemini API Key:</strong>
            <div class="api-key" id="geminiApiKey">Loading...</div>
        </div>

        <h2>🧪 API Tests</h2>
        <button onclick="testGoogleSearch()">Test Google Search API</button>
        <button onclick="testGeminiAPI()">Test Gemini AI API</button>
        <button onclick="testBothAPIs()">Test Both APIs</button>

        <div id="results"></div>
    </div>

    <script>
        // Load configuration from the main script
        const CONFIG = {
            GOOGLE_SEARCH_API_KEY: 'AIzaSyDZHL5hT78whoMeQgtj76DYpO9SoPWcFN0',
            GOOGLE_SEARCH_CX: '61201925358ea4e83',
            GEMINI_API_KEY: 'AIzaSyCMLLE7tBI9RSt7X43tNPfi3dSLVRqFNow'
        };

        // Display configuration
        document.getElementById('googleApiKey').textContent = CONFIG.GOOGLE_SEARCH_API_KEY;
        document.getElementById('googleCx').textContent = CONFIG.GOOGLE_SEARCH_CX;
        document.getElementById('geminiApiKey').textContent = CONFIG.GEMINI_API_KEY;

        function showResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testGoogleSearch() {
            showResult('🔍 Testing Google Search API...', 'info');
            
            try {
                const testQuery = 'test search';
                const apiUrl = `https://www.googleapis.com/customsearch/v1?key=${CONFIG.GOOGLE_SEARCH_API_KEY}&cx=${CONFIG.GOOGLE_SEARCH_CX}&q=${encodeURIComponent(testQuery)}&num=1`;
                
                const response = await fetch(apiUrl);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.items && data.items.length > 0) {
                    showResult(`✅ Google Search API is working! Found ${data.searchInformation?.totalResults || 'unknown'} results for "${testQuery}"`, 'success');
                } else {
                    showResult('⚠️ Google Search API responded but returned no results', 'error');
                }
                
            } catch (error) {
                showResult(`❌ Google Search API test failed: ${error.message}`, 'error');
            }
        }

        async function testGeminiAPI() {
            showResult('🤖 Testing Gemini AI API...', 'info');
            
            try {
                const testPrompt = 'Say "Hello, API test successful!" if you can read this.';
                const payload = {
                    contents: [{ role: "user", parts: [{ text: testPrompt }] }]
                };
                
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${CONFIG.GEMINI_API_KEY}`;
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                    const responseText = result.candidates[0].content.parts[0].text;
                    showResult(`✅ Gemini AI API is working! Response: "${responseText}"`, 'success');
                } else {
                    showResult('⚠️ Gemini AI API responded but with unexpected format', 'error');
                }
                
            } catch (error) {
                showResult(`❌ Gemini AI API test failed: ${error.message}`, 'error');
            }
        }

        async function testBothAPIs() {
            showResult('🚀 Testing both APIs sequentially...', 'info');
            await testGoogleSearch();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            await testGeminiAPI();
            showResult('✨ All API tests completed!', 'info');
        }

        // Auto-run basic validation on page load
        window.onload = function() {
            showResult('🔧 API Configuration loaded successfully', 'success');
            
            // Validate API key formats
            if (CONFIG.GOOGLE_SEARCH_API_KEY.startsWith('AIza') && CONFIG.GOOGLE_SEARCH_API_KEY.length > 30) {
                showResult('✅ Google Search API Key format looks valid', 'success');
            } else {
                showResult('⚠️ Google Search API Key format may be invalid', 'error');
            }
            
            if (CONFIG.GOOGLE_SEARCH_CX.length > 10) {
                showResult('✅ Google Custom Search CX format looks valid', 'success');
            } else {
                showResult('⚠️ Google Custom Search CX format may be invalid', 'error');
            }
            
            if (CONFIG.GEMINI_API_KEY.startsWith('AIza') && CONFIG.GEMINI_API_KEY.length > 30) {
                showResult('✅ Gemini API Key format looks valid', 'success');
            } else {
                showResult('⚠️ Gemini API Key format may be invalid', 'error');
            }
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Clone with Gemini AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Results Page Header -->
    <div id="resultsHeader" class="hidden">
        <div class="logo-container">
            <a href="#" onclick="showMainSearchPage(); return false;" class="flex items-center">
                <span class="text-2xl font-medium text-[#4285F4]">G</span><span class="text-2xl font-medium text-[#EA4335]">o</span><span class="text-2xl font-medium text-[#FBBC05]">o</span><span class="text-2xl font-medium text-[#4285F4]">g</span><span class="text-2xl font-medium text-[#34A853]">l</span><span class="text-2xl font-medium text-[#EA4335]">e</span>
            </a>
        </div>
        <form id="resultsSearchForm" class="flex-grow">
            <div class="search-input-container">
                <svg class="search-icon h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" /></svg>
                <input type="text" id="resultsSearchInput" class="search-input">
            </div>
        </form>
    </div>

    <!-- Search Tabs & Tools Container -->
    <div id="searchTabsContainer" class="hidden">
        <div id="searchTabs">
            <div class="tab-item active" data-tab-name="all" onclick="handleTabClick(event, 'all')"><i class="fas fa-search"></i>All</div>
            <div class="tab-item" data-tab-name="images" onclick="handleTabClick(event, 'images')"><i class="fas fa-image"></i>Images</div>
            <div class="tab-item" data-tab-name="news" onclick="handleTabClick(event, 'news')"><i class="fas fa-newspaper"></i>News</div>
            <div class="tab-item" data-tab-name="videos" onclick="handleTabClick(event, 'videos')"><i class="fas fa-video"></i>Videos</div>
            <div class="tab-item" data-tab-name="web" onclick="handleTabClick(event, 'web')"><i class="fas fa-globe-americas"></i>Web</div>
            <div class="tab-item" data-tab-name="forums" onclick="handleTabClick(event, 'forums')"><i class="fas fa-comments"></i>Forums</div>
        </div>
        <div id="toolsButton" onclick="handleToolsClick()">Tools</div>
    </div>

    <div class="main-content-centered">
        <!-- Initial Search Section -->
        <div id="initialSearchSection">
            <div class="mb-7 text-center">
                <span class="text-8xl font-medium text-[#4285F4]">G</span><span class="text-8xl font-medium text-[#EA4335]">o</span><span class="text-8xl font-medium text-[#FBBC05]">o</span><span class="text-8xl font-medium text-[#4285F4]">g</span><span class="text-8xl font-medium text-[#34A853]">l</span><span class="text-8xl font-medium text-[#EA4335]">e</span>
            </div>
            <form id="searchForm" class="w-full max-w-xl">
                <div class="search-input-container mb-5">
                    <svg class="search-icon h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" /></svg>
                    <input type="text" id="searchInput" class="search-input" autofocus>
                </div>
                <div class="search-buttons flex justify-center">
                    <button type="submit">Google Search</button>
                    <button type="button" id="talkToAIButton"><i class="fas fa-microphone-alt"></i>Talk to AI</button>
                </div>
            </form>
        </div>

        <!-- Wrapper for stats, results, and messages on results page -->
        <div id="resultsAreaContainer" class="results-area-container hidden">
            <div id="resultsStatsContainer">
                <div id="resultsStats" class="results-stats"></div>
            </div>
            <div id="aiSummarySection" class="hidden">
                <h3><i class="fas fa-magic"></i> ✨ AI-Generated Summary</h3>
                <div id="aiSummaryContent"></div>
                <div id="geminiSummaryLoader" class="gemini-loading-spinner hidden"></div>
            </div>
            <div id="aiTalkResponseSection" class="hidden">
                <h3><i class="fas fa-comments"></i> AI Response</h3>
                <div id="aiTalkResponseContent"></div>
                <div id="geminiTalkLoader" class="gemini-loading-spinner hidden"></div>
            </div>
            <div id="messageArea" class="message-area"></div>
            <div id="resultsContainer" class="results-container"></div>

            <div id="paaContainer" class="hidden">
                <h3><i class="fas fa-question-circle"></i> People Also Ask</h3>
                <div id="paaContent"></div>
                <div id="geminiPAALoader" class="gemini-loading-spinner hidden"></div>
            </div>

            <div id="relatedSearchesContainer" class="hidden">
                <h3><i class="fas fa-wand-magic-sparkles"></i> ✨ AI-Powered Related Searches</h3>
                <div id="relatedSearchesContent"></div>
                <div id="geminiRelatedLoader" class="gemini-loading-spinner hidden"></div>
            </div>

            <div id="paginationContainer" class="hidden">
                <a href="#" id="prevPageLink" class="pagination-nav-button">
                    <i class="fas fa-chevron-left"></i><span class="nav-text">Previous</span>
                </a>
                <div class="page-numbers-container" id="pageNumbers"></div>
                <a href="#" id="nextPageLink" class="pagination-nav-button">
                    <span class="nav-text">Next</span><i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>

        <div id="loadingIndicator" class="hidden loading-spinner"></div>
    </div>

    <footer class="footer"><p>India</p></footer>

    <script src="script.js"></script>
</body>
</html>

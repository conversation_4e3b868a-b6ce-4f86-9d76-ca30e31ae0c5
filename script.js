// --- Configuration ---
const GOOGLE_SEARCH_API_KEY = 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM' ;
const GOOGLE_SEARCH_CX = '30a8567a4e17d49d2';
const GEMINI_API_KEY = "AIzaSyBLZPBER1n_SpWx5ZbK9ToP3VeD10K5IHI";
const RESULTS_PER_PAGE = 10;
const MAX_VISIBLE_PAGES = 10;
// --- End Configuration ---

let currentPage = 1;
let originalQuery = '';
let currentQueryForAPI = '';
let totalResults = 0;
let currentSearchResultsForSummary = [];
let currentActiveTab = 'all';

const initialSearchSection = document.getElementById('initialSearchSection');
const searchForm = document.getElementById('searchForm');
const searchInput = document.getElementById('searchInput');
const talkToAIButton = document.getElementById('talkToAIButton');

const resultsHeader = document.getElementById('resultsHeader');
const resultsSearchForm = document.getElementById('resultsSearchForm');
const resultsSearchInput = document.getElementById('resultsSearchInput');
const searchTabsContainer = document.getElementById('searchTabsContainer');

const resultsAreaContainer = document.getElementById('resultsAreaContainer');
const resultsStats = document.getElementById('resultsStats');
const aiSummarySection = document.getElementById('aiSummarySection');
const aiSummaryContent = document.getElementById('aiSummaryContent');
const geminiSummaryLoader = document.getElementById('geminiSummaryLoader');

const aiTalkResponseSection = document.getElementById('aiTalkResponseSection');
const aiTalkResponseContent = document.getElementById('aiTalkResponseContent');
const geminiTalkLoader = document.getElementById('geminiTalkLoader');

const resultsContainer = document.getElementById('resultsContainer');
const loadingIndicator = document.getElementById('loadingIndicator');
const messageArea = document.getElementById('messageArea');

const paaContainer = document.getElementById('paaContainer');
const paaContent = document.getElementById('paaContent');
const geminiPAALoader = document.getElementById('geminiPAALoader');

const paaContainerMain = document.getElementById('paaContainerMain');
const paaContentMain = document.getElementById('paaContentMain');
const geminiPAALoaderMain = document.getElementById('geminiPAALoaderMain');

const relatedSearchesContainer = document.getElementById('relatedSearchesContainer');
const relatedSearchesContent = document.getElementById('relatedSearchesContent');
const geminiRelatedLoader = document.getElementById('geminiRelatedLoader');

const paginationContainer = document.getElementById('paginationContainer');
const prevPageLink = document.getElementById('prevPageLink');
const nextPageLink = document.getElementById('nextPageLink');
const pageNumbers = document.getElementById('pageNumbers');

// Event listeners
searchForm.addEventListener('submit', (event) => {
    event.preventDefault();
    originalQuery = searchInput.value.trim();
    currentActiveTab = 'all';
    setActiveTabUI('all');
    if (originalQuery) { currentPage = 1; fetchCurrentTabData(); }
});

talkToAIButton.addEventListener('click', () => {
    originalQuery = searchInput.value.trim();
    if (originalQuery) {
        handleTalkToAI(originalQuery);
    } else {
        showMessage("Please enter something to talk about.");
    }
});

resultsSearchForm.addEventListener('submit', (event) => {
    event.preventDefault();
    originalQuery = resultsSearchInput.value.trim();
    if (document.body.classList.contains('ai-talk-active')) {
         if (originalQuery) handleTalkToAI(originalQuery);
         else showMainSearchPage();
    } else {
        currentActiveTab = 'all';
        setActiveTabUI('all');
        if (originalQuery) { currentPage = 1; fetchCurrentTabData(); }
        else { showMainSearchPage(); }
    }
});

prevPageLink.addEventListener('click', (e) => {
    e.preventDefault();
    if (currentPage > 1 && !prevPageLink.classList.contains('disabled')) {
        currentPage--;
        fetchCurrentTabData();
    }
});

nextPageLink.addEventListener('click', (e) => {
    e.preventDefault();
    if (((currentPage * RESULTS_PER_PAGE) < totalResults) && !nextPageLink.classList.contains('disabled')) {
        currentPage++;
        fetchCurrentTabData();
    }
});

function showMainSearchPage() {
    resultsHeader.classList.add('hidden');
    searchTabsContainer.classList.add('hidden');
    initialSearchSection.classList.remove('hidden');
    resultsAreaContainer.classList.add('hidden');
    aiTalkResponseSection.classList.add('hidden');
    document.body.classList.remove('results-active', 'ai-talk-active');

    clearPreviousResultsAndAI();

    originalQuery = ''; currentQueryForAPI = '';
    searchInput.value = ''; resultsSearchInput.value = '';
    searchInput.focus();
    currentActiveTab = 'all';
}

function showResultsPageLayout(queryToDisplay, isAiTalkMode = false) {
    initialSearchSection.classList.add('hidden');
    resultsHeader.classList.remove('hidden');
    resultsSearchInput.value = queryToDisplay;

    if (isAiTalkMode) {
        document.body.classList.add('ai-talk-active');
        document.body.classList.remove('results-active');
        searchTabsContainer.classList.add('hidden');
        resultsAreaContainer.classList.add('hidden');
        aiTalkResponseSection.classList.remove('hidden');
    } else {
        document.body.classList.add('results-active');
        document.body.classList.remove('ai-talk-active');
        searchTabsContainer.classList.remove('hidden');
        resultsAreaContainer.classList.remove('hidden');
        aiTalkResponseSection.classList.add('hidden');
        aiSummarySection.classList.add('hidden');
        paaContainer.classList.add('hidden');
        relatedSearchesContainer.classList.add('hidden');
    }
}

function clearPreviousResultsAndAI() {
    resultsContainer.innerHTML = '';
    messageArea.textContent = '';
    resultsStats.textContent = '';
    paginationContainer.classList.add('hidden');

    aiSummarySection.classList.add('hidden');
    aiSummaryContent.innerHTML = '';

    paaContainer.classList.add('hidden');
    paaContent.innerHTML = '';

    paaContainerMain.classList.add('hidden');
    paaContentMain.innerHTML = '';

    relatedSearchesContainer.classList.add('hidden');
    relatedSearchesContent.innerHTML = '';

    aiTalkResponseContent.innerHTML = '';

    currentSearchResultsForSummary = [];
}

async function fetchGoogleResults(queryForAPI, isFeelingLucky = false, startIndex = 1, searchTypeParam = null) {
    currentQueryForAPI = queryForAPI;

    if (GOOGLE_SEARCH_API_KEY === 'YOUR_GOOGLE_API_KEY' || GOOGLE_SEARCH_CX === 'YOUR_CUSTOM_SEARCH_ENGINE_ID' || !GOOGLE_SEARCH_API_KEY || !GOOGLE_SEARCH_CX) {
        showMessage('Please configure your Google Search API Key and CX ID in the script.');
        if (!document.body.classList.contains('results-active') && !document.body.classList.contains('ai-talk-active')) { showMainSearchPage(); }
        else { resultsSearchInput.value = originalQuery; }
        return;
    }

    showResultsPageLayout(originalQuery, false);
    clearPreviousResultsAndAI();
    loadingIndicator.classList.remove('hidden');

    let apiUrl = `https://www.googleapis.com/customsearch/v1?key=${GOOGLE_SEARCH_API_KEY}&cx=${GOOGLE_SEARCH_CX}&q=${encodeURIComponent(queryForAPI)}&start=${startIndex}&num=${RESULTS_PER_PAGE}`;
    if (searchTypeParam === 'image') {
        apiUrl += `&searchType=image`;
    }

    try {
        const response = await fetch(apiUrl);
        if (!response.ok) {
            const errorData = await response.json();
            console.error('Google Search API Error:', errorData);
            let errorMessageText = `Error fetching results: ${response.status} ${response.statusText}`;
            if (errorData.error && errorData.error.message) { errorMessageText += ` - ${errorData.error.message}`; }
            throw new Error(errorMessageText);
        }
        const data = await response.json();

        if (data.searchInformation) {
            totalResults = parseInt(data.searchInformation.totalResults);
            resultsStats.textContent = `About ${data.searchInformation.formattedTotalResults} results (${data.searchInformation.formattedSearchTime} seconds)`;
        } else { totalResults = 0; resultsStats.textContent = ''; }

        if (data.items && data.items.length > 0) {
            if (isFeelingLucky) { window.location.href = data.items[0].link; return; }

            displayGoogleResults(data.items, searchTypeParam);
            updatePagination();

            if (currentActiveTab === 'all') {
                currentSearchResultsForSummary = data.items.map(item => ({ title: item.title, snippet: item.snippet }));
                fetchAndDisplayAISummary(originalQuery, currentSearchResultsForSummary);
                fetchAIRelatedSearches(originalQuery);
                fetchAIPeopleAlsoAsk(originalQuery);

                // Show PAA in sidebar for desktop, hide main PAA
                if (window.innerWidth > 1024) {
                    paaContainer.classList.remove('hidden');
                    paaContainerMain.classList.add('hidden');
                    fetchAIPeopleAlsoAskSidebar(originalQuery);
                } else {
                    paaContainer.classList.add('hidden');
                    paaContainerMain.classList.remove('hidden');
                }
            } else {
                // For other tabs, hide both PAA containers
                paaContainer.classList.add('hidden');
                paaContainerMain.classList.add('hidden');
            }
        } else {
            if (startIndex === 1) { showMessage(`No ${searchTypeParam || 'web'} results found for "${originalQuery}".`); }
            else { showMessage('No more results.'); }
            totalResults = 0; updatePagination();
        }
    } catch (error) {
        console.error('Failed to fetch Google search results:', error);
        showMessage(`${error.message}. Check console.`);
        totalResults = 0; updatePagination();
    } finally { loadingIndicator.classList.add('hidden'); }
}

function displayGoogleResults(items, searchType) {
    resultsContainer.innerHTML = '';
    resultsContainer.className = 'results-container'; // Reset class list
    if (searchType === 'image') {
        resultsContainer.classList.add('image-results-grid');
    }

    if (searchType === 'image') {
        items.forEach(item => {
            const imageItemDiv = document.createElement('div');
            imageItemDiv.classList.add('image-result-item');
            const linkElement = document.createElement('a');
            linkElement.href = item.image.contextLink;
            linkElement.target = '_blank';
            const imgElement = document.createElement('img');
            imgElement.src = item.image.thumbnailLink || item.link;
            imgElement.alt = item.title;
            imgElement.loading = "lazy";
            imgElement.onerror = () => { imgElement.src = 'https://placehold.co/150x120/eee/ccc?text=No+Image'; };
            const titleDiv = document.createElement('div');
            titleDiv.classList.add('image-title');
            titleDiv.textContent = item.title;
            linkElement.appendChild(imgElement);
            linkElement.appendChild(titleDiv);
            imageItemDiv.appendChild(linkElement);
            resultsContainer.appendChild(imageItemDiv);
        });
    } else {
        items.forEach(item => {
            const resultItemDiv = document.createElement('div');
            resultItemDiv.classList.add('result-item');
            const urlLineDiv = document.createElement('div');
            urlLineDiv.classList.add('url-line');
            const faviconImg = document.createElement('img');
            faviconImg.classList.add('favicon');
            faviconImg.loading = "lazy";
            faviconImg.onerror = () => { faviconImg.style.display = 'none'; };
            try {
                const domain = new URL(item.link).hostname;
                faviconImg.src = `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
            } catch (e) { faviconImg.style.display = 'none'; }
            const urlElement = document.createElement('span');
            urlElement.classList.add('url');
            try {
                const linkUrl = new URL(item.link);
                const domainPart = `<span class="domain">${linkUrl.hostname}</span>`;
                let pathPart = '';
                if (linkUrl.pathname && linkUrl.pathname !== '/') {
                    const pathSegments = linkUrl.pathname.split('/').filter(Boolean).map(part => part.length > 20 ? part.substring(0,17) + '...' : part );
                    if (pathSegments.length > 0) {
                        pathPart = ` <span class="path">› ${pathSegments.join(' › ')}</span>`;
                    }
                }
                urlElement.innerHTML = domainPart + pathPart;
            } catch (e) { urlElement.textContent = item.displayLink || item.link; }
            urlLineDiv.appendChild(faviconImg); urlLineDiv.appendChild(urlElement);
            const titleElement = document.createElement('a');
            titleElement.classList.add('title'); titleElement.href = item.link;
            titleElement.textContent = item.title; titleElement.target = '_blank';
            const snippetElement = document.createElement('p');
            snippetElement.classList.add('snippet');
            snippetElement.innerHTML = item.htmlSnippet || item.snippet;

            // Add ELI5 button for 'all' and 'web' tabs
            if (currentActiveTab === 'all' || currentActiveTab === 'web') {
                const eli5Button = document.createElement('button');
                eli5Button.classList.add('eli5-button');
                eli5Button.innerHTML = `<i class="fas fa-brain"></i> ELI5`;
                eli5Button.onclick = (event) => handleELI5Click(event, item.title, item.htmlSnippet || item.snippet, resultItemDiv);
                resultItemDiv.appendChild(urlLineDiv);
                resultItemDiv.appendChild(titleElement);
                resultItemDiv.appendChild(snippetElement);
                resultItemDiv.appendChild(eli5Button); // Add button after snippet
                const eli5Wrapper = document.createElement('div'); // Wrapper for ELI5 content
                eli5Wrapper.classList.add('eli5-content-wrapper');
                resultItemDiv.appendChild(eli5Wrapper);
            } else {
                resultItemDiv.appendChild(urlLineDiv);
                resultItemDiv.appendChild(titleElement);
                resultItemDiv.appendChild(snippetElement);
            }
            resultsContainer.appendChild(resultItemDiv);
        });
    }
}

function showMessage(text, isError = false) {
    messageArea.textContent = text;
    messageArea.style.color = isError ? 'red' : '#70757a';
}

function updatePagination() {
    pageNumbers.innerHTML = '';
    if (totalResults <= RESULTS_PER_PAGE) {
        paginationContainer.classList.add('hidden');
        return;
    }
    paginationContainer.classList.remove('hidden');
    const totalPages = Math.ceil(totalResults / RESULTS_PER_PAGE);

    prevPageLink.classList.toggle('disabled', currentPage === 1);
    nextPageLink.classList.toggle('disabled', currentPage === totalPages || totalPages === 0);

    const googleLetters = ['G', 'o', 'o', 'o', 'o', 'o', 'g', 'l', 'e'];
    const letterClasses = ['letter-g', 'letter-o1', 'letter-o2', 'letter-o3', 'letter-o2', 'letter-o1', 'letter-g2', 'letter-l', 'letter-e'];

    let startPage = Math.max(1, currentPage - Math.floor(MAX_VISIBLE_PAGES / 2));
    let endPage = Math.min(totalPages, startPage + MAX_VISIBLE_PAGES - 1);
    if (endPage - startPage + 1 < MAX_VISIBLE_PAGES && startPage > 1) {
        startPage = Math.max(1, endPage - MAX_VISIBLE_PAGES + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageLink = document.createElement('a');
        pageLink.href = '#';
        pageLink.classList.add('page-number');
        if (i < googleLetters.length + 1 && currentPage === i) {
             pageLink.textContent = googleLetters[i-1];
             if(letterClasses[i-1]) pageLink.classList.add(letterClasses[i-1]);
        } else { pageLink.textContent = i; }
        if (i === currentPage) { pageLink.classList.add('current'); }
        pageLink.addEventListener('click', (e) => {
            e.preventDefault(); if (i === currentPage) return;
            currentPage = i;
            fetchCurrentTabData();
        });
        pageNumbers.appendChild(pageLink);
    }
}

async function fetchAndDisplayAISummary(query, resultsForSummary) {
    if (resultsForSummary.length === 0) {
        aiSummarySection.classList.add('hidden');
        return;
    }
    aiSummarySection.classList.remove('hidden');
    aiSummaryContent.textContent = '';
    geminiSummaryLoader.classList.remove('hidden');

    const resultsText = resultsForSummary.map(r => `Title: ${r.title}\nSnippet: ${r.snippet}`).join('\n\n');
    const prompt = `Based on the following search results for the query "${query}", please provide a brief summary (2-4 sentences) of the main topics or findings. Focus on the collective information rather than individual links:\n\n${resultsText}`;

    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = { contents: chatHistory };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
        if (!response.ok) {
            const errorData = await response.json(); console.error("Gemini API Error (Summary):", errorData);
            throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
        }
        const result = await response.json();
        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {
            const summaryText = result.candidates[0].content.parts[0].text;
            aiSummaryContent.textContent = summaryText;
        } else { throw new Error("Unexpected response structure from Gemini API for summary."); }
    } catch (error) {
        console.error("Error summarizing results with Gemini:", error);
        aiSummaryContent.textContent = `Error generating summary: ${error.message}`;
        aiSummaryContent.style.color = 'red';
    } finally {
        geminiSummaryLoader.classList.add('hidden');
    }
}

async function fetchAIRelatedSearches(query) {
    relatedSearchesContainer.classList.remove('hidden');
    relatedSearchesContent.innerHTML = '';
    geminiRelatedLoader.classList.remove('hidden');
    const prompt = `Given the search query "${query}", suggest 4 highly relevant related search queries that a user might find helpful.`;
    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = {
            contents: chatHistory,
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: { type: "OBJECT", properties: { "related_queries": { type: "ARRAY", items: { type: "STRING" } } } }
            }
        };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
        if (!response.ok) {
            const errorData = await response.json(); console.error("Gemini API Error (Related Searches):", errorData);
            throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
        }
        const result = await response.json();
        if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts[0].text) {
            const jsonResponse = JSON.parse(result.candidates[0].content.parts[0].text);
            if (jsonResponse.related_queries && jsonResponse.related_queries.length > 0) {
                displayAIRelatedSearches(jsonResponse.related_queries);
            } else { relatedSearchesContent.textContent = "No AI related searches found."; }
        } else { throw new Error("Unexpected response structure from Gemini API for related searches.");}
    } catch (error) {
        console.error("Error fetching AI related searches:", error);
        relatedSearchesContent.textContent = `Could not load AI related searches: ${error.message}`;
        relatedSearchesContent.style.color = 'red';
    } finally { geminiRelatedLoader.classList.add('hidden'); }
}

function displayAIRelatedSearches(queries) {
    relatedSearchesContent.innerHTML = '';
    queries.forEach(queryText => {
        const item = document.createElement('span');
        item.classList.add('related-search-item');
        item.textContent = queryText;
        item.onclick = () => performRelatedSearch(item);
        relatedSearchesContent.appendChild(item);
    });
}

async function fetchAIPeopleAlsoAsk(query) {
    paaContainer.classList.remove('hidden');
    paaContent.innerHTML = '';
    geminiPAALoader.classList.remove('hidden');
    const prompt = `For the search query "${query}", generate 3-4 common questions that people also ask.`;
    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = {
            contents: chatHistory,
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: { type: "OBJECT", properties: { "questions": { type: "ARRAY", items: { type: "STRING" } } } }
            }
        };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
        if (!response.ok) {
            const errorData = await response.json(); console.error("Gemini API Error (PAA Questions):", errorData);
            throw new Error(`Gemini API error for PAA questions: ${errorData.error?.message || response.statusText}`);
        }
        const result = await response.json();
        if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts[0].text) {
            const jsonResponse = JSON.parse(result.candidates[0].content.parts[0].text);
            if (jsonResponse.questions && jsonResponse.questions.length > 0) {
                displayAIPeopleAlsoAsk(jsonResponse.questions);
            } else { paaContent.textContent = "No 'People Also Ask' questions found."; }
        } else { throw new Error("Unexpected response structure from Gemini API for PAA questions."); }
    } catch (error) {
        console.error("Error fetching PAA questions:", error);
        paaContent.textContent = `Could not load PAA questions: ${error.message}`;
        paaContent.style.color = 'red';
    } finally { geminiPAALoader.classList.add('hidden'); }
}

function displayAIPeopleAlsoAsk(questions) {
    paaContent.innerHTML = '';
    questions.forEach(questionText => {
        const paaItemDiv = document.createElement('div'); paaItemDiv.classList.add('paa-item');
        const questionDiv = document.createElement('div'); questionDiv.classList.add('paa-question');
        questionDiv.innerHTML = `<span>${questionText}</span><i class="fas fa-chevron-down toggle-icon"></i>`;
        const answerDiv = document.createElement('div'); answerDiv.classList.add('paa-answer');
        paaItemDiv.appendChild(questionDiv); paaItemDiv.appendChild(answerDiv);
        paaContent.appendChild(paaItemDiv);
        questionDiv.addEventListener('click', async () => {
            const isOpen = paaItemDiv.classList.toggle('open');
            if (isOpen && !answerDiv.dataset.loaded) {
                const answerLoader = document.createElement('div');
                answerLoader.classList.add('gemini-loading-spinner', 'inline-gemini-loader');
                answerDiv.innerHTML = ''; answerDiv.appendChild(answerLoader);
                try {
                    const answerPrompt = `Answer the following question concisely, as if it were a "People Also Ask" result on Google: "${questionText}"`;
                    const chatHistory = [{ role: "user", parts: [{ text: answerPrompt }] }];
                    const payload = { contents: chatHistory };
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
                    const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Gemini API error for PAA answer: ${errorData.error?.message || response.statusText}`);
                    }
                    const result = await response.json();
                    if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0].text) {
                        answerDiv.textContent = result.candidates[0].content.parts[0].text;
                        answerDiv.dataset.loaded = "true";
                    } else { throw new Error("Unexpected response for PAA answer."); }
                } catch (error) {
                    console.error("Error fetching PAA answer:", error);
                    answerDiv.textContent = `Error loading answer: ${error.message}`;
                    answerDiv.style.color = 'red';
                } finally { answerLoader.remove(); }
            }
        });
    });
}

async function fetchAIPeopleAlsoAskSidebar(query) {
    paaContainer.classList.remove('hidden');
    paaContent.innerHTML = '';
    geminiPAALoader.classList.remove('hidden');
    const prompt = `For the search query "${query}", generate 3-4 common questions that people also ask.`;
    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = {
            contents: chatHistory,
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: { type: "OBJECT", properties: { "questions": { type: "ARRAY", items: { type: "STRING" } } } }
            }
        };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
        if (!response.ok) {
            const errorData = await response.json(); console.error("Gemini API Error (Sidebar PAA Questions):", errorData);
            throw new Error(`Gemini API error for sidebar PAA questions: ${errorData.error?.message || response.statusText}`);
        }
        const result = await response.json();
        if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts[0].text) {
            const jsonResponse = JSON.parse(result.candidates[0].content.parts[0].text);
            if (jsonResponse.questions && jsonResponse.questions.length > 0) {
                displayAIPeopleAlsoAsk(jsonResponse.questions);
            } else { paaContent.textContent = "No 'People Also Ask' questions found."; }
        } else { throw new Error("Unexpected response structure from Gemini API for sidebar PAA questions."); }
    } catch (error) {
        console.error("Error fetching sidebar PAA questions:", error);
        paaContent.textContent = `Could not load PAA questions: ${error.message}`;
        paaContent.style.color = 'red';
    } finally { geminiPAALoader.classList.add('hidden'); }
}

async function handleTalkToAI(query) {
    showResultsPageLayout(query, true);
    clearPreviousResultsAndAI();
    aiTalkResponseSection.classList.remove('hidden');
    aiTalkResponseContent.innerHTML = '';
    geminiTalkLoader.classList.remove('hidden');

    const prompt = `You are a helpful conversational AI. Respond to the following user query: "${query}"`;
    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = { contents: chatHistory };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error("Gemini API Error (Talk to AI):", errorData);
            throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
        }
        const result = await response.json();

        if (result.candidates && result.candidates.length > 0 &&
            result.candidates[0].content && result.candidates[0].content.parts &&
            result.candidates[0].content.parts.length > 0) {
            const aiResponseText = result.candidates[0].content.parts[0].text;
            aiTalkResponseContent.textContent = aiResponseText;
        } else {
            throw new Error("Unexpected response structure from Gemini API for AI talk.");
        }
    } catch (error) {
        console.error("Error talking to AI with Gemini:", error);
        aiTalkResponseContent.textContent = `Error getting AI response: ${error.message}`;
        aiTalkResponseContent.style.color = 'red';
    } finally {
        geminiTalkLoader.classList.add('hidden');
    }
}

async function handleELI5Click(_event, title, snippet, resultItemDiv) {
    const eli5Wrapper = resultItemDiv.querySelector('.eli5-content-wrapper');
    if (!eli5Wrapper) return;

    const isOpen = eli5Wrapper.classList.toggle('open');

    if (isOpen && !eli5Wrapper.dataset.loaded) {
        const loader = document.createElement('div');
        loader.classList.add('gemini-loading-spinner', 'inline-gemini-loader');
        eli5Wrapper.innerHTML = ''; // Clear previous content
        eli5Wrapper.appendChild(loader);

        const prompt = `Explain the following topic like I'm 5 years old, based on this title and snippet. Keep it very simple and short (1-2 sentences if possible):\n\nTitle: ${title}\nSnippet: ${snippet}`;
        try {
            const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
            const payload = { contents: chatHistory };
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
            const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Gemini API ELI5 error: ${errorData.error?.message || response.statusText}`);
            }
            const result = await response.json();
            if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0].text) {
                eli5Wrapper.textContent = result.candidates[0].content.parts[0].text;
                eli5Wrapper.dataset.loaded = "true";
            } else {
                throw new Error("Unexpected response for ELI5.");
            }
        } catch (error) {
            console.error("Error fetching ELI5:", error);
            eli5Wrapper.textContent = `ELI5 Error: ${error.message}`;
            eli5Wrapper.style.color = 'red';
        } finally {
            loader.remove();
        }
    }
}

function setActiveTabUI(tabName) {
    document.querySelectorAll('#searchTabs .tab-item').forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.tabName === tabName) {
            tab.classList.add('active');
        }
    });
}

function fetchCurrentTabData() {
    if (!originalQuery) return;

    if (document.body.classList.contains('ai-talk-active')) {
        document.body.classList.remove('ai-talk-active');
        aiTalkResponseSection.classList.add('hidden');
        currentActiveTab = 'all';
        setActiveTabUI('all');
    }

    // Handle AI tab specially
    if (currentActiveTab === 'ai') {
        showAIOnlyResults();
        return;
    }

    let queryToUseForAPI = originalQuery;
    let searchType = null;

    if (currentActiveTab === 'images') {
        searchType = 'image';
    } else if (currentActiveTab === 'news') {
        queryToUseForAPI = originalQuery + " latest news";
    } else if (currentActiveTab === 'videos') {
        queryToUseForAPI = originalQuery + " videos";
    } else if (currentActiveTab === 'forums') {
        queryToUseForAPI = originalQuery + " site:reddit.com OR site:quora.com OR inurl:forum OR inurl:viewthread OR intitle:forum";
    }
    // For 'all' and 'web', queryToUseForAPI remains originalQuery and searchType is null

    fetchGoogleResults(queryToUseForAPI, false, (currentPage - 1) * RESULTS_PER_PAGE + 1, searchType);
}

function showAIOnlyResults() {
    showResultsPageLayout(originalQuery, false);
    clearPreviousResultsAndAI();

    // Show AI summary
    aiSummarySection.classList.remove('hidden');
    aiSummaryContent.textContent = '';
    geminiSummaryLoader.classList.remove('hidden');

    // Show People Also Ask in main content area for AI tab
    paaContainerMain.classList.remove('hidden');
    paaContentMain.innerHTML = '';
    geminiPAALoaderMain.classList.remove('hidden');

    // Hide sidebar PAA for AI tab
    paaContainer.classList.add('hidden');

    // Hide pagination and other elements
    paginationContainer.classList.add('hidden');
    relatedSearchesContainer.classList.add('hidden');

    // Generate AI content
    generateAISummaryForAITab(originalQuery);
    generatePAAForAITab(originalQuery);
}

async function generateAISummaryForAITab(query) {
    const prompt = `Provide a comprehensive AI-generated summary about "${query}". Include key facts, important information, and relevant details that would be helpful to someone searching for this topic. Make it informative and well-structured.`;

    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = { contents: chatHistory };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
        }

        const result = await response.json();
        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {
            const summaryText = result.candidates[0].content.parts[0].text;
            aiSummaryContent.textContent = summaryText;
        } else {
            throw new Error("Unexpected response structure from Gemini API for AI summary.");
        }
    } catch (error) {
        console.error("Error generating AI summary:", error);
        aiSummaryContent.textContent = `Error generating AI summary: ${error.message}`;
        aiSummaryContent.style.color = 'red';
    } finally {
        geminiSummaryLoader.classList.add('hidden');
    }
}

async function generatePAAForAITab(query) {
    const prompt = `For the topic "${query}", generate 5-6 comprehensive questions that people commonly ask, along with detailed answers. Format as JSON with questions and answers.`;

    try {
        const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
        const payload = {
            contents: chatHistory,
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "OBJECT",
                    properties: {
                        "qa_pairs": {
                            type: "ARRAY",
                            items: {
                                type: "OBJECT",
                                properties: {
                                    "question": { type: "STRING" },
                                    "answer": { type: "STRING" }
                                }
                            }
                        }
                    }
                }
            }
        };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Gemini API error for AI PAA: ${errorData.error?.message || response.statusText}`);
        }

        const result = await response.json();
        if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts[0].text) {
            const jsonResponse = JSON.parse(result.candidates[0].content.parts[0].text);
            if (jsonResponse.qa_pairs && jsonResponse.qa_pairs.length > 0) {
                displayPAAForAITab(jsonResponse.qa_pairs);
            } else {
                paaContentMain.textContent = "No questions found.";
            }
        } else {
            throw new Error("Unexpected response structure from Gemini API for AI PAA.");
        }
    } catch (error) {
        console.error("Error fetching AI PAA:", error);
        paaContentMain.textContent = `Could not load questions: ${error.message}`;
        paaContentMain.style.color = 'red';
    } finally {
        geminiPAALoaderMain.classList.add('hidden');
    }
}

function displayPAAForAITab(qaPairs) {
    paaContentMain.innerHTML = '';
    qaPairs.forEach(qa => {
        const paaItemDiv = document.createElement('div');
        paaItemDiv.classList.add('paa-item');

        const questionDiv = document.createElement('div');
        questionDiv.classList.add('paa-question');
        questionDiv.innerHTML = `<span>${qa.question}</span><i class="fas fa-chevron-down toggle-icon"></i>`;

        const answerDiv = document.createElement('div');
        answerDiv.classList.add('paa-answer');
        answerDiv.textContent = qa.answer;
        answerDiv.dataset.loaded = "true";

        paaItemDiv.appendChild(questionDiv);
        paaItemDiv.appendChild(answerDiv);
        paaContentMain.appendChild(paaItemDiv);

        questionDiv.addEventListener('click', () => {
            paaItemDiv.classList.toggle('open');
        });
    });
}

function handleTabClick(_event, tabName) {
    if (!originalQuery) {
        showMessage("Please perform a search first.");
        setActiveTabUI(currentActiveTab || 'all');
        return;
    }
    if (document.body.classList.contains('ai-talk-active')) {
        document.body.classList.remove('ai-talk-active');
        aiTalkResponseSection.classList.add('hidden');
        resultsAreaContainer.classList.remove('hidden');
    }

    currentActiveTab = tabName;
    setActiveTabUI(tabName);
    currentPage = 1;
    fetchCurrentTabData();
}

function handleToolsClick() {
    showMessage("Tools functionality is not implemented in this clone.");
}

function performRelatedSearch(element) {
    originalQuery = element.textContent;
    searchInput.value = originalQuery;
    resultsSearchInput.value = originalQuery;
    currentActiveTab = 'all';
    setActiveTabUI('all');
    currentPage = 1;
    fetchCurrentTabData();
}

// Handle window resize for responsive PAA placement
window.addEventListener('resize', () => {
    if (currentActiveTab === 'all' && originalQuery && document.body.classList.contains('results-active')) {
        if (window.innerWidth > 1024) {
            // Desktop: show PAA in sidebar
            paaContainer.classList.remove('hidden');
            paaContainerMain.classList.add('hidden');
            if (paaContent.innerHTML === '') {
                fetchAIPeopleAlsoAskSidebar(originalQuery);
            }
        } else {
            // Mobile: show PAA in main content
            paaContainer.classList.add('hidden');
            paaContainerMain.classList.remove('hidden');
            if (paaContentMain.innerHTML === '') {
                fetchAIPeopleAlsoAsk(originalQuery);
            }
        }
    }
});

// Initial setup
showMainSearchPage();

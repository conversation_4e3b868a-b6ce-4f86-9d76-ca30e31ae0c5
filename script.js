// Google Clone JavaScript Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const searchInput = document.querySelector('.search-input');
    const searchButtons = document.querySelectorAll('.btn');
    const voiceSearchBtn = document.querySelector('.voice-search');
    const cameraSearchBtn = document.querySelector('.camera-search');
    const appsMenu = document.querySelector('.apps-menu');
    const profilePic = document.querySelector('.profile-pic');

    // Search functionality
    function performSearch(query, isLucky = false) {
        if (!query.trim()) {
            searchInput.focus();
            return;
        }

        // Encode the search query for URL
        const encodedQuery = encodeURIComponent(query.trim());
        
        if (isLucky) {
            // "I'm Feeling Lucky" - redirect to first result (simulate)
            window.open(`https://www.google.com/search?q=${encodedQuery}&btnI=1`, '_blank');
        } else {
            // Regular Google search
            window.open(`https://www.google.com/search?q=${encodedQuery}`, '_blank');
        }
    }

    // Handle search input
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch(this.value);
        }
    });

    // Handle search buttons
    searchButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
            const query = searchInput.value;
            const isLucky = index === 1; // Second button is "I'm Feeling Lucky"
            performSearch(query, isLucky);
        });
    });

    // Voice search functionality
    voiceSearchBtn.addEventListener('click', function() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onstart = function() {
                searchInput.placeholder = 'Listening...';
                voiceSearchBtn.style.backgroundColor = 'rgba(66, 133, 244, 0.1)';
            };

            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                searchInput.value = transcript;
                searchInput.focus();
            };

            recognition.onerror = function(event) {
                console.error('Speech recognition error:', event.error);
                searchInput.placeholder = '';
            };

            recognition.onend = function() {
                searchInput.placeholder = '';
                voiceSearchBtn.style.backgroundColor = '';
            };

            recognition.start();
        } else {
            alert('Speech recognition is not supported in your browser.');
        }
    });

    // Camera search functionality (placeholder)
    cameraSearchBtn.addEventListener('click', function() {
        alert('Camera search functionality would be implemented here. This would typically open a camera interface or file picker for image search.');
    });

    // Apps menu functionality
    appsMenu.addEventListener('click', function() {
        // Create a simple dropdown menu
        const existingMenu = document.querySelector('.apps-dropdown');
        if (existingMenu) {
            existingMenu.remove();
            return;
        }

        const dropdown = document.createElement('div');
        dropdown.className = 'apps-dropdown';
        dropdown.innerHTML = `
            <div class="apps-grid">
                <a href="https://mail.google.com" target="_blank" class="app-item">
                    <div class="app-icon" style="background-color: #ea4335;">📧</div>
                    <span>Gmail</span>
                </a>
                <a href="https://drive.google.com" target="_blank" class="app-item">
                    <div class="app-icon" style="background-color: #4285f4;">💾</div>
                    <span>Drive</span>
                </a>
                <a href="https://www.youtube.com" target="_blank" class="app-item">
                    <div class="app-icon" style="background-color: #ff0000;">📺</div>
                    <span>YouTube</span>
                </a>
                <a href="https://maps.google.com" target="_blank" class="app-item">
                    <div class="app-icon" style="background-color: #34a853;">🗺️</div>
                    <span>Maps</span>
                </a>
                <a href="https://photos.google.com" target="_blank" class="app-item">
                    <div class="app-icon" style="background-color: #fbbc05;">📷</div>
                    <span>Photos</span>
                </a>
                <a href="https://translate.google.com" target="_blank" class="app-item">
                    <div class="app-icon" style="background-color: #4285f4;">🌐</div>
                    <span>Translate</span>
                </a>
            </div>
        `;

        // Style the dropdown
        dropdown.style.cssText = `
            position: absolute;
            top: 50px;
            right: 60px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            padding: 20px;
            z-index: 1000;
            min-width: 300px;
        `;

        // Style the apps grid
        const style = document.createElement('style');
        style.textContent = `
            .apps-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
            }
            .app-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-decoration: none;
                color: #202124;
                padding: 10px;
                border-radius: 8px;
                transition: background-color 0.2s;
            }
            .app-item:hover {
                background-color: rgba(0,0,0,0.04);
            }
            .app-icon {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                margin-bottom: 8px;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(dropdown);

        // Close dropdown when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdown.contains(e.target) && !appsMenu.contains(e.target)) {
                    dropdown.remove();
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }, 0);
    });

    // Profile picture functionality
    profilePic.addEventListener('click', function() {
        alert('Profile menu would be implemented here with account options, sign out, etc.');
    });

    // Search suggestions (basic implementation)
    let suggestionTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(suggestionTimeout);
        const query = this.value.trim();
        
        if (query.length > 0) {
            suggestionTimeout = setTimeout(() => {
                // In a real implementation, this would fetch suggestions from Google's API
                console.log('Would fetch suggestions for:', query);
            }, 300);
        }
    });

    // Random search suggestions for demo
    const demoSuggestions = [
        'weather today',
        'latest news',
        'how to code',
        'best restaurants near me',
        'funny cat videos',
        'learn javascript',
        'healthy recipes',
        'travel destinations 2024'
    ];

    // Add a random suggestion on page load
    const randomSuggestion = demoSuggestions[Math.floor(Math.random() * demoSuggestions.length)];
    searchInput.placeholder = `Try searching: "${randomSuggestion}"`;

    // Focus on search input when page loads
    searchInput.focus();

    // Handle language links
    const languageLinks = document.querySelectorAll('.languages a');
    languageLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            alert(`Language change to ${this.textContent} would be implemented here.`);
        });
    });

    // Handle footer links
    const footerLinks = document.querySelectorAll('.footer-links a');
    footerLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            alert(`${this.textContent} page would be implemented here.`);
        });
    });

    // Add some keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Focus search input with Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
            searchInput.select();
        }
        
        // Clear search with Escape
        if (e.key === 'Escape' && document.activeElement === searchInput) {
            searchInput.value = '';
        }
    });

    console.log('Google Clone loaded successfully!');
});
